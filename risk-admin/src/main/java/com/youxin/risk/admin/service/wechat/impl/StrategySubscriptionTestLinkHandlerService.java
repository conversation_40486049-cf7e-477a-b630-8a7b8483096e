package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 测试统一配置链接生成处理服务
 */
@Service
public class StrategySubscriptionTestLinkHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            String strategyType = getRequiredParam(params, "strategyType");
            String monitorId = getRequiredParam(params, "monitorId");

            LoggerProxy.info("processMessage", logger,
                    "测试统一配置链接生成, strategyType={}, monitorId={}", strategyType, monitorId);

            StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
            if (mapping == null) {
                throw new IllegalArgumentException("策略类型未配置: " + strategyType);
            }

            MonitorConfig monitor = null;
            if (mapping.getMonitors() != null) {
                monitor = mapping.getMonitors().stream()
                        .filter(m -> monitorId.equals(m.getId()))
                        .findFirst()
                        .orElse(null);
            }

            if (monitor == null) {
                throw new IllegalArgumentException("监控配置未找到: " + monitorId);
            }

            // 生成动态链接
            String dynamicLink = strategyMonitorConfigService.generateMonitorLink(monitor);

            if (StringUtils.isNotBlank(dynamicLink)) {
                LoggerProxy.info("processMessage", logger,
                        "统一配置链接生成成功, strategyType={}, monitorId={}, link={}",
                        strategyType, monitorId, dynamicLink);
                
                // 返回包含监控配置和生成链接的详细信息
                return new TestLinkResult(monitor, dynamicLink);
            } else {
                LoggerProxy.warn("processMessage", logger,
                        "统一配置链接生成失败, strategyType={}, monitorId={}", strategyType, monitorId);
                throw new RuntimeException("链接生成失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger,
                    "测试统一配置链接生成异常, params=" + params, e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 测试统一配置链接生成\n\n");
        
        String strategyType = getParam(params, "strategyType");
        String monitorId = getParam(params, "monitorId");
        
        markdownContent.append("> **策略类型:** ").append(infoMessage(strategyType)).append("\n");
        markdownContent.append("> **监控ID:** ").append(infoMessage(monitorId)).append("\n");
        
        if (result instanceof TestLinkResult) {
            TestLinkResult testResult = (TestLinkResult) result;
            MonitorConfig monitor = testResult.getMonitor();
            String dynamicLink = testResult.getDynamicLink();
            
            markdownContent.append("> **操作结果:** ").append(successMessage("链接生成成功")).append("\n\n");
            
            markdownContent.append("**监控配置信息:**\n");
            markdownContent.append(String.format("- **监控名称:** %s\n", monitor.getName()));
            markdownContent.append(String.format("- **事件代码:** %s\n", monitor.getEventCode()));
            markdownContent.append(String.format("- **动态链接:** %s\n", monitor.getUseDynamicLink() ? "是" : "否"));
            
            if (monitor.getFrequencyMinutes() != null) {
                markdownContent.append(String.format("- **推送频率:** %d 分钟\n", monitor.getFrequencyMinutes()));
            }
            if (monitor.getTotalPushes() != null) {
                markdownContent.append(String.format("- **总推送次数:** %d 次\n", monitor.getTotalPushes()));
            }
            
            markdownContent.append("\n**生成的监控链接:**\n");
            markdownContent.append(String.format("[点击查看监控](%s)\n", dynamicLink));
            
            markdownContent.append("\n> 链接生成测试成功！您可以点击上方链接验证监控数据是否正常显示。");
            
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("链接生成失败")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_TEST_LINK.getCode();
    }

    /**
     * 测试链接结果封装类
     */
    public static class TestLinkResult {
        private final MonitorConfig monitor;
        private final String dynamicLink;

        public TestLinkResult(MonitorConfig monitor, String dynamicLink) {
            this.monitor = monitor;
            this.dynamicLink = dynamicLink;
        }

        public MonitorConfig getMonitor() {
            return monitor;
        }

        public String getDynamicLink() {
            return dynamicLink;
        }
    }
}
