package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 手动触发推送处理服务
 */
@Service
public class StrategySubscriptionManualPushHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            LoggerProxy.info("processMessage", logger, "手动触发推送");

            // 获取待推送记录
            List<SubscriptionRecord> pendingSubscriptions = strategySubscriptionService.getPendingSubscriptions();
            
            if (pendingSubscriptions.isEmpty()) {
                return "没有需要推送的记录";
            }

            // 这里可以调用定时任务的逻辑
            String result = String.format("找到 %d 条待推送记录，请查看日志了解详细执行情况", pendingSubscriptions.size());
            
            LoggerProxy.info("processMessage", logger, "手动触发推送完成, pendingCount={}", pendingSubscriptions.size());
            
            return result;

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger, "手动触发推送异常", e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 手动触发推送\n\n");
        
        if (result instanceof String) {
            String message = (String) result;
            markdownContent.append("> **操作结果:** ").append(successMessage("推送触发成功")).append("\n");
            markdownContent.append("> **详细信息:** ").append(infoMessage(message)).append("\n");
            
            if (message.contains("没有需要推送的记录")) {
                markdownContent.append("\n当前没有待推送的订阅记录。");
            } else {
                markdownContent.append("\n推送任务已启动，请查看系统日志了解详细执行情况。");
            }
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("推送触发异常")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_MANUAL_PUSH.getCode();
    }
}
