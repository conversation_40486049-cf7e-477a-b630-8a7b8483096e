package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 查询用户订阅记录处理服务
 */
@Service
public class StrategySubscriptionListHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            String userId = getRequiredParam(params, "userId");
            String strategyType = getParam(params, "strategyType");

            LoggerProxy.info("processMessage", logger, 
                    "查询用户订阅记录, userId={}, strategyType={}", userId, strategyType);

            List<SubscriptionRecord> subscriptions = strategySubscriptionService.getUserSubscriptions(userId, strategyType);
            
            LoggerProxy.info("processMessage", logger, 
                    "查询用户订阅记录完成, userId={}, strategyType={}, count={}", 
                    userId, strategyType, subscriptions.size());
            
            return subscriptions;

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger, 
                    "查询用户订阅记录异常, params=" + params, e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 用户订阅记录查询\n\n");
        
        String userId = getParam(params, "userId");
        String strategyType = getParam(params, "strategyType");
        
        markdownContent.append("> **用户ID:** ").append(infoMessage(userId)).append("\n");
        if (strategyType != null && !strategyType.trim().isEmpty()) {
            markdownContent.append("> **策略类型:** ").append(infoMessage(strategyType)).append("\n");
        }
        
        if (result instanceof List) {
            @SuppressWarnings("unchecked")
            List<SubscriptionRecord> subscriptions = (List<SubscriptionRecord>) result;
            
            markdownContent.append("> **订阅数量:** ").append(infoMessage(String.valueOf(subscriptions.size()))).append("\n\n");
            
            if (subscriptions.isEmpty()) {
                markdownContent.append("暂无订阅记录。");
            } else {
                markdownContent.append("**订阅详情:**\n");
                for (int i = 0; i < subscriptions.size(); i++) {
                    SubscriptionRecord record = subscriptions.get(i);
                    markdownContent.append(String.format("%d. **%s**\n", i + 1, record.getStrategyType()));
                    markdownContent.append(String.format("   - 监控ID: %s\n", record.getMonitorId()));
                    markdownContent.append(String.format("   - 已推送: %d/%d 次\n", 
                            record.getSentPushes(), record.getTotalPushes()));
                    markdownContent.append(String.format("   - 状态: %s\n", 
                            record.getIsCompleted() ? "已完成" : "进行中"));
                }
            }
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("查询异常")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_LIST.getCode();
    }
}
