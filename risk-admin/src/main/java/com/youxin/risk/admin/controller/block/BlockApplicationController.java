package com.youxin.risk.admin.controller.block;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.admin.controller.BaseController;
import com.youxin.risk.admin.controller.block.request.EliminateAlarmRequest;
import com.youxin.risk.admin.controller.block.request.EngineEventRequest;
import com.youxin.risk.admin.controller.block.request.EngineForwardRequest;
import com.youxin.risk.admin.controller.block.request.EngineRetryRequest;
import com.youxin.risk.admin.controller.block.request.HHForwardRequest;
import com.youxin.risk.admin.controller.block.request.HHRetryRequest;
import com.youxin.risk.admin.controller.block.request.RRDForwardRequest;
import com.youxin.risk.admin.controller.block.request.TriggerDpCallbackRequest;
import com.youxin.risk.admin.controller.block.response.EngineBlockedData;
import com.youxin.risk.admin.controller.block.response.EngineEventBlockedData;
import com.youxin.risk.admin.controller.block.response.HHBlockedDataTask;
import com.youxin.risk.admin.controller.block.response.RRDBlockDataRecord;
import com.youxin.risk.admin.dao.admin.AdminDiServiceMapper;
import com.youxin.risk.admin.dao.admin.AdminEventNodeRelationMapper;
import com.youxin.risk.admin.dao.di.AdminDiTaskMapper;
import com.youxin.risk.admin.dao.engine.EngineAsyncRequestLogMapper;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminDiService;
import com.youxin.risk.admin.model.AdminDiTask;
import com.youxin.risk.admin.model.AdminEventNodeRelation;
import com.youxin.risk.admin.model.BlockedEngineEvent;
import com.youxin.risk.admin.model.EngineEvent;
import com.youxin.risk.admin.model.GwRequest;
import com.youxin.risk.admin.model.GwRetryRequestBody;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.model.vo.BlockEventResultVo;
import com.youxin.risk.admin.model.vo.GwRequestVo;
import com.youxin.risk.admin.service.EngineEventService;
import com.youxin.risk.admin.service.GwRequestService;
import com.youxin.risk.admin.service.block.qw.BlockHandlerServiceHelp;
import com.youxin.risk.admin.service.impl.RedisDelTmpCacheService;
import com.youxin.risk.admin.service.wechat.WeChatHandlerServiceHelp;
import com.youxin.risk.admin.vo.EventCodeVo;

import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.model.EngineAsyncRequestLog;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.youxin.risk.commons.constants.ApolloNamespace.commonSpace;

/**
 * 处理卡单接口
 *
 * <AUTHOR>
 * @date 2019-06-04
 */
@Controller
@RequestMapping("/block")
@Api(value = "/block", description = "卡单相关操作")
public class BlockApplicationController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BlockApplicationController.class);

    private final static int HTTP_REQUEST_TIME_OUT = 30000;
    private final static String UNKNOWN_RECORD_TYPE = "unknown";

    @Value("${dp.trigger.callback.url}")
    private String dpCallbackUrl;

    @Value("${ra.base.url}")
    private String raBaseUrl;

    @Value("${rrd.service.url}")
    private String rrdServiceUrl;

    @Value("${gw.service.url}")
    private String gwServiceUrl;

    @Value("${gateway.url}")
    private String gatewayUrl;

    @Resource
    private GwRequestService gwRequestService;

    @Value("${channel.forward.url}")
    private String channelForwardUrl;

    private Executor singlePool = Executors.newSingleThreadExecutor();

    @Autowired
    private AdminEventNodeRelationMapper adminEventNodeRelationMapper;

    @Resource
    private EngineEventService engineEventService;

    @Resource
    private EngineAsyncRequestLogMapper engineAsyncRequestLogMapper;

    @Autowired
    private AdminDiServiceMapper adminDiServiceMapper;

    @Autowired
    private AdminDiTaskMapper adminDiTaskMapper;

    @Value("${risk.verify.url}")
    private String riskVerifyUrl;

    private String verifyRemoveAlert = "/switch/removeSubmitAlert/";

    private String pressTestPre = "pressTest";

    @Autowired
    private RedisDelTmpCacheService redisDelTmpCacheService;

    @Autowired
    private StrategySubscriptionService strategySubscriptionService;

    @Autowired
    private StrategyMonitorConfigService strategyMonitorConfigService;

    @Autowired
    private WechatMessageService wechatMessageService;

    @RequestMapping(value = "/dp/triggerCallback", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "触发数据平台回调", notes = "触发数据平台回调", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> triggerCallback(@RequestBody List<TriggerDpCallbackRequest> dpCallbackRequests) {
        List<BlockEventResultVo> resultVos = engineEventService.triggerCallback(dpCallbackRequests, dpCallbackUrl, HTTP_REQUEST_TIME_OUT);
        if (resultVos == null) {
            return buildSuccessResponse("当前有任务还没处理完，请稍候");
        }
        if (CollectionUtils.isNotEmpty(resultVos)) {
            return buildSuccessResponse(resultVos.toString());
        }
        return buildSuccessResponse("触发数据平台回调成功");
    }


    @RequestMapping(value = "/hh/list", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "好分期卡单数据列表", notes = "好分期卡单数据列表", response = HHBlockedDataTask.class, responseContainer = "list")
    public ResponseEntity<?> hhBlockList() {
        String response = SyncHTTPRemoteAPI.get(raBaseUrl + "/block/list", HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(response);
        }

        List<HHBlockedDataTask> result = Lists.newArrayList();
        JSONArray blockedList = responseObj.getJSONObject("data").getJSONArray("blockedList");
        for (Object o : blockedList) {
            HHBlockedDataTask hhBlockedDataTask = JsonUtils.toObject(JsonUtils.toJson(o), HHBlockedDataTask.class);
            hhBlockedDataTask.setRecordType(hhBlockedDataTask.getRecordType());
            result.add(hhBlockedDataTask);
        }
        return buildSuccessResponse(result);
    }

    @RequestMapping(value = "/hh/retry", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "触发好分期重试", notes = "触发好分期重试", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> hhRetry(@RequestBody HHRetryRequest request) {
        LoggerProxy.info(LOGGER, "request={}", request);
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getThirdPartyType()));
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getUserKey()));
        Map<String, String> params = Maps.newHashMap();
        params.put("thirdPartyType", request.getThirdPartyType());
        params.put("userKey", request.getUserKey());
        String response = SyncHTTPRemoteAPI.postJson(raBaseUrl + "/block/retry", JsonUtils.toJson(params), HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(responseObj.getString("message"));
        }
        return buildSuccessResponse("重试成功");
    }

    @RequestMapping(value = "/hh/forward", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改好分期数据为已获取状态", notes = "修改好分期数据为已获取状态", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> hhForward(@RequestBody HHForwardRequest request) {
        LoggerProxy.info(LOGGER, "request={}", request);
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getUserKey()));
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getLoanKey()));
        Map<String, String> params = Maps.newHashMap();
        params.put("userKey", request.getUserKey());
        params.put("loanKey", request.getLoanKey());
        String response = SyncHTTPRemoteAPI.postJson(raBaseUrl + "/block/forward", JsonUtils.toJson(params), HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(responseObj.getString("message"));
        }
        return buildSuccessResponse("成功");
    }

    @RequestMapping(value = "/rrd/list", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "人人贷卡单数据列表", notes = "人人贷卡单数据列表", response = RRDBlockDataRecord.class, responseContainer = "list")
    public ResponseEntity<?> rrdBlockList() {
        String response = SyncHTTPRemoteAPI.get(rrdServiceUrl + "/block/list", HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(response);
        }

        List<RRDBlockDataRecord> result = Lists.newArrayList();
        JSONArray blockedList = responseObj.getJSONArray("data");
        for (Object o : blockedList) {
            RRDBlockDataRecord blockedDataTask = JsonUtils.toObject(JsonUtils.toJson(o), RRDBlockDataRecord.class);
            String recordType = recordType(blockedDataTask.getServiceCode());
            blockedDataTask.setRecordType(UNKNOWN_RECORD_TYPE.equals(recordType) ? blockedDataTask.getThirdPartyType() : recordType);
            result.add(blockedDataTask);
        }
        return buildSuccessResponse(result);
    }

    @RequestMapping(value = "/rrd/forward", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改人人贷数据为已获取状态", notes = "修改人人贷数据为已获取状态", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> rrdForward(@RequestBody RRDForwardRequest request) {

        LoggerProxy.info(LOGGER, "request={}", request);
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getApplicationId()));
        Map<String, String> params = Maps.newHashMap();
        params.put("applicationId", request.getApplicationId());
        String response = SyncHTTPRemoteAPI.postJson(rrdServiceUrl + "/block/forward", JsonUtils.toJson(params), HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(responseObj.getString("message"));
        }
        return buildSuccessResponse("forward success");
    }

    @RequestMapping(value = "/engine/list", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "引擎卡单数据列表", notes = "引擎卡单数据列表", response = EngineBlockedData.class, responseContainer = "list")
    public ResponseEntity<?> engineBlockList() {
        List<EngineEvent> engineEventList = engineEventService.getBlockedList();
        if (CollectionUtils.isEmpty(engineEventList)) {
            return buildSuccessResponse(Collections.EMPTY_LIST);
        }
        List<String> sessionIdList = engineEventList.stream().map(EngineEvent::getSessionId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<EngineAsyncRequestLog> blockedList = engineAsyncRequestLogMapper.getBlockedList(sessionIdList);
        if (CollectionUtils.isEmpty(blockedList)) {
            return buildSuccessResponse(Collections.EMPTY_LIST);
        }
        List<String> requestIdList = blockedList.stream().map(EngineAsyncRequestLog::getAsyncRequestId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<AdminDiTask> adminDiTaskList = adminDiTaskMapper.selectByRequestIdList(requestIdList);

        Map<String, AdminDiTask> adminDiTaskMap = adminDiTaskList.stream().collect(Collectors.toMap(AdminDiTask::getRequestId, adminDiTask -> adminDiTask));
        Map<String, EngineEvent> engineEventMap = engineEventList.stream().collect(Collectors.toMap(EngineEvent::getSessionId, engineEvent -> engineEvent));

        List<EngineBlockedData> result = Lists.newArrayList();
        for (EngineAsyncRequestLog log : blockedList) {
            EngineEvent engineEvent = engineEventMap.get(log.getSessionId());
            EngineBlockedData engineBlockedData = new EngineBlockedData();
            engineBlockedData.setEngineRequestLogId(log.getAsyncRequestId());
            engineBlockedData.setSessionId(log.getSessionId());
            engineBlockedData.setEventCode(engineEvent.getEventCode());
            engineBlockedData.setInsId(engineEvent.getProcessInstanceId());
            engineBlockedData.setThirdPartyType(log.getDataCode());
            String recordType = UNKNOWN_RECORD_TYPE;
            AdminDiTask adminDiTask = adminDiTaskMap.get(log.getAsyncRequestId());
            if (!Objects.isNull(adminDiTask)) {
                recordType = adminDiTaskMap.get(log.getAsyncRequestId()).getTaskType();
            }
            engineBlockedData.setRecordType(recordType);
            engineBlockedData.setJobId(log.getJobId());
            engineBlockedData.setUserKey(log.getUserKey());
            engineBlockedData.setStatus(log.getStatus());
            engineBlockedData.setCreateTime(log.getCreateTime());
            engineBlockedData.setUpdateTime(log.getUpdateTime());
            result.add(engineBlockedData);
        }
        return buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/retry", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "数据源重新调用", notes = "数据源重新调用", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> engineRetry(@RequestBody List<EngineRetryRequest> request) {
        LoggerProxy.info(LOGGER, "retryDataRequest, request:{}", request);
        List<String> requestIdList = Lists.newArrayList();
        request.forEach(retryRequest -> {
            requestIdList.add(retryRequest.getRequestId());
        });
        String result = engineEventService.engineRetry(requestIdList);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/forward", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "强制前进", notes = "强制前进", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> engineForward(@RequestBody List<EngineForwardRequest> request) {
        LoggerProxy.info(LOGGER, "request={}", request);
        List<String> requestLogIdList = Lists.newArrayList();
        List<String> sessionIdList = Lists.newArrayList();
        request.forEach(re -> {
            requestLogIdList.add(re.getEngineRequestLogId());
            sessionIdList.add(re.getSessionId());
        });
        if (CollectionUtils.isEmpty(requestLogIdList) || CollectionUtils.isEmpty(sessionIdList)) {
            return buildSuccessResponse("请求参数缺少");
        }
        String result = engineEventService.engineForward(requestLogIdList, sessionIdList, channelForwardUrl, HTTP_REQUEST_TIME_OUT);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    private String recordType(String serviceCode) {
        AdminDiService adminDiService = adminDiServiceMapper.selectEntityByServiceCode(serviceCode);
        if (Objects.isNull(adminDiService)) {
            return UNKNOWN_RECORD_TYPE;
        }
        return adminDiService.getTaskType();
    }

    @RequestMapping(value = "/engine/event/list", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "引擎卡单流程列表", notes = "引擎卡单流程列表", response = EngineEventBlockedData.class, responseContainer = "list")
    public ResponseEntity<?> engineEventBlockList() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<BlockedEngineEvent> blockedEngineEventList = engineEventService.getBlockedEventList();
        if (CollectionUtils.isEmpty(blockedEngineEventList)) {
            return buildSuccessResponse(Collections.EMPTY_LIST);
        }
        List<EngineEventBlockedData> result = Lists.newArrayList();
        for (BlockedEngineEvent blockedEngineEvent : blockedEngineEventList) {
            EngineEventBlockedData engineEventBlockedData = new EngineEventBlockedData();
            engineEventBlockedData.setId(blockedEngineEvent.getId());
            engineEventBlockedData.setCreateTime(blockedEngineEvent.getCreateTime());
            engineEventBlockedData.setUpdateTime(blockedEngineEvent.getUpdateTime());
            engineEventBlockedData.setCurrentNodeId(blockedEngineEvent.getCurrentNodeId());
            engineEventBlockedData.setEventCode(blockedEngineEvent.getEventCode());
            engineEventBlockedData.setLoanKey(blockedEngineEvent.getLoanKey());
            engineEventBlockedData.setProcessInstanceId(blockedEngineEvent.getProcessInstanceId());
            engineEventBlockedData.setProcessDefId(blockedEngineEvent.getProcessDefId());
            engineEventBlockedData.setUserKey(blockedEngineEvent.getUserKey());
            engineEventBlockedData.setStatus(blockedEngineEvent.getStatus());
            engineEventBlockedData.setSessionId(blockedEngineEvent.getSessionId());
            result.add(engineEventBlockedData);
        }
        return buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/event/processRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新发起流程", notes = "重新发起流程", response = String.class)
    public ResponseEntity<?> processRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIdList = Lists.newArrayList();
        engineEventRequests.forEach(request -> {
            sessionIdList.add(request.getSessionId());
        });
        String result = engineEventService.setFailed(sessionIdList);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/event/processAndThirdpartyRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新发起流程并且获取三方", notes = "重新发起流程并且获取三方", response = String.class)
    public ResponseEntity<?> processAndThirdpartyRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIdList = Lists.newArrayList();
        engineEventRequests.forEach(request -> {
            sessionIdList.add(request.getSessionId());
        });
        String result = engineEventService.setFailedAndDeleteThirdparty(sessionIdList);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/event/updateCurrentTime", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "流程事件更新到当前时间", notes = "流程事件更新到当前时间", response = String.class)
    public ResponseEntity<?> updateCurrentTime(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIdList = Lists.newArrayList();
        engineEventRequests.forEach(request -> {
            sessionIdList.add(request.getSessionId());
        });
        String result = engineEventService.updateCreateTimeBySessionIds(sessionIdList);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    /*-----------------------------------修改为分页 update by fanlingyin*/
    @RequestMapping(value = "/engine/event/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "引擎卡单流程分页列表", notes = "引擎卡单分页列表", response = EngineEventBlockedData.class, responseContainer = "page")
    public ResponseEntity<?> engineEventBlockPageList(@RequestBody JSONObject params) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        LOGGER.info("/engine/event/page params = {}", params.toJSONString());
        Page<EngineEventBlockedData> pageResult = engineEventService.getBlockedEventPageList(params);
        return buildSuccessResponse(pageResult);
    }

    @RequestMapping(value = "/engine/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "引擎卡单数据分页列表", notes = "引擎卡单数据分页列表", response = EngineBlockedData.class, responseContainer = "page")
    public ResponseEntity<?> engineBlockPageList(@RequestBody JSONObject params) {
        Page<EngineBlockedData> pageResult = engineEventService.getBlockedPageList(params);
        return buildSuccessResponse(pageResult);

    }

    @RequestMapping(value = "/hh/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "好分期卡单数据分页列表", notes = "好分期卡单数据分页列表", response = HHBlockedDataTask.class, responseContainer = "page")
    public ResponseEntity<?> hhBlockLPageist(@RequestBody JSONObject params) {
        String response = SyncHTTPRemoteAPI.get(raBaseUrl + "/block/list", HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(response);
        }

        List<HHBlockedDataTask> result = Lists.newArrayList();
        JSONArray blockedList = responseObj.getJSONObject("data").getJSONArray("blockedList");
        for (Object o : blockedList) {
            HHBlockedDataTask hhBlockedDataTask = JsonUtils.toObject(JsonUtils.toJson(o), HHBlockedDataTask.class);
            hhBlockedDataTask.setRecordType(hhBlockedDataTask.getRecordType());
            result.add(hhBlockedDataTask);
        }
        int pageNo = params.containsKey("pageNum") ? (Integer) params.get("pageNum") : 1;
        int pageSize = params.containsKey("pageSize") ? (Integer) params.get("pageSize") : 20;
        PageHelper.startPage(pageNo, pageSize);
        PageInfo pageResult = new PageInfo(result);
        return buildSuccessResponse(pageResult);
    }

    @RequestMapping(value = "/rrd/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "人人贷卡单数据分页列表", notes = "人人贷卡单数据分页列表", response = RRDBlockDataRecord.class, responseContainer = "page")
    public ResponseEntity<?> rrdBlockPageList(@RequestBody JSONObject params) {
        String response = SyncHTTPRemoteAPI.get(rrdServiceUrl + "/block/list", HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(response);
        }

        List<RRDBlockDataRecord> result = Lists.newArrayList();
        JSONArray blockedList = responseObj.getJSONArray("data");
        for (Object o : blockedList) {
            RRDBlockDataRecord blockedDataTask = JsonUtils.toObject(JsonUtils.toJson(o), RRDBlockDataRecord.class);
            String recordType = recordType(blockedDataTask.getServiceCode());
            blockedDataTask.setRecordType(UNKNOWN_RECORD_TYPE.equals(recordType) ? blockedDataTask.getThirdPartyType() : recordType);
            result.add(blockedDataTask);
        }
        int pageNo = params.containsKey("pageNum") ? (Integer) params.get("pageNum") : 1;
        int pageSize = params.containsKey("pageSize") ? (Integer) params.get("pageSize") : 20;
        PageHelper.startPage(pageNo, pageSize);
        PageInfo pageResult = new PageInfo(result);
        return buildSuccessResponse(pageResult);
    }

    @RequestMapping(value = "/engine/event/gatewayRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新进件", notes = "重新进件", response = ResponseEntity.class)
    public ResponseEntity<?> gatewayRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {
        String result = gwRequestService.gatewayRetry(engineEventRequests, gwServiceUrl, gatewayUrl, HTTP_REQUEST_TIME_OUT);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    @RequestMapping(value = "/gw/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "Gateway卡单流程分页列表", notes = "Gateway卡单流程分页列表", response = GwRequest.class, responseContainer = "page")
    public ResponseEntity<?> gwBlockPageList(@RequestBody JSONObject params) {
        Page<GwRequestVo> pageResult = gwRequestService.getBlockedGwRequestPageList(params);
        return buildSuccessResponse(pageResult);
    }

    @RequestMapping(value = "/event/code/all", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getEventCodeList() {
        List<AdminEventNodeRelation> adminEventNodeRelationList = adminEventNodeRelationMapper.groupByEventCodeAndStep();
        Set<String> codeSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(adminEventNodeRelationList)) {
            adminEventNodeRelationList.forEach(re -> {
                codeSet.add(re.getEventCode());
            });
        }
        List<EventCodeVo> eventCodeList = new ArrayList<>();
        codeSet.forEach(re -> {
            EventCodeVo eventCodeVo = new EventCodeVo();
            eventCodeVo.setEventCode(re);
            eventCodeVo.setEventCodeLabel(re);
            eventCodeList.add(eventCodeVo);
        });
        return buildSuccessResponse(eventCodeList);
    }

    @RequestMapping(value = "/event/codeAndDesc/all", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getEventCodeAndDescList() {
        List<AdminEventNodeRelation> adminEventNodeRelationList = adminEventNodeRelationMapper.groupByEventCodeAndStep();
        Set<String> codeSet = new TreeSet<>();
        if (CollectionUtils.isNotEmpty(adminEventNodeRelationList)) {
            adminEventNodeRelationList.forEach(re -> {
                codeSet.add(re.getEventCode()+"_"+re.getEventName());
            });
        }
        List<EventCodeVo> eventCodeList = new ArrayList<>();
        codeSet.forEach(re -> {
            EventCodeVo eventCodeVo = new EventCodeVo();
            eventCodeVo.setEventCode(re.split("_")[0]);
            eventCodeVo.setEventCodeLabel(re);
            eventCodeList.add(eventCodeVo);
        });
        return buildSuccessResponse(eventCodeList);
    }

    //消除报警
    @RequestMapping(value = "/event/eliminate/alarm", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> eliminateAlarm(@RequestBody List<EliminateAlarmRequest> eliminateAlarmRequests) {
        List<String> sessionIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(eliminateAlarmRequests)) {
            eliminateAlarmRequests.forEach(eliminateAlarmRequest -> {
                gwRequestService.deleteMiniBySessionId(Collections.singletonList(eliminateAlarmRequest.getSessionId()));
                if ("haoHuanVerify".equals(eliminateAlarmRequest.getEventCode())) {
                    sessionIds.add(eliminateAlarmRequest.getSessionId());
                }
            });
            if (CollectionUtils.isNotEmpty(sessionIds)) {
                List<GwRetryRequestBody> gwRetryRequestBodyList = gwRequestService.getRequestBodyBySessionIds(sessionIds);
                gwRetryRequestBodyList.forEach(gwRetryRequestBody -> {
                    Integer loanId = JSONObject.parseObject(gwRetryRequestBody.getRequestBody())
                            .getJSONObject("data").getJSONObject("message").getInteger("loanId");
                    String url = riskVerifyUrl + verifyRemoveAlert + loanId;
                    LoggerProxy.info("BlockApplicationController.eliminateAlarm", LOGGER, "verifyRemoveAlertUrl={},sessionId={}", url, gwRetryRequestBody.getSessionId());
                    String response = SyncHTTPRemoteAPI.get(url, HTTP_REQUEST_TIME_OUT);
                    LoggerProxy.info("BlockApplicationController.eliminateAlarm", LOGGER, "sessionId={},response={}", gwRetryRequestBody.getSessionId(), response);
                    if (StringUtils.isNotBlank(response)) {
                        JSONObject responseObject = JSONObject.parseObject(response);
                        Integer retCode = responseObject.getInteger("status");
                        if (0 != retCode) {
                            LoggerProxy.error("BlockApplicationController.eliminateAlarm", LOGGER, "sessionId={},response={}", gwRetryRequestBody.getSessionId(), response);
                        }
                    }
                });
            }
        } else {
            return buildErrorResponse("param is empty!");
        }
        return buildSuccessResponse("succeed to eliminate alarm!");
    }

    @RequestMapping(value = "/delete/pressTest", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "Gateway卡单流程分页列表", notes = "Gateway卡单流程分页列表", response = GwRequest.class, responseContainer = "page")
    public ResponseEntity<?> deletePressTest(@RequestParam String eventCode) {
        if (StringUtils.isNotBlank(eventCode) && eventCode.startsWith(pressTestPre)) {
            singlePool.execute(() -> {
                gwRequestService.deletePressTest(eventCode);
            });
        }
        return buildSuccessResponse("deletePressTest data started!");
    }

    @RequestMapping(value = "/risk/apollo/value", method = {RequestMethod.GET})
    @ResponseBody
    public String getApolloValue(String key, String nameSpace) {
        try {
            if (StringUtils.isBlank(nameSpace)) {
                nameSpace = commonSpace;
            }
            return NacosClient.getByNameSpace(nameSpace, key, "未获取到apollo配置");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return e.getMessage();
        }
    }

    @RequestMapping(value = "/risk/sysconfig/value", method = {RequestMethod.GET})
    @ResponseBody
    public String getSysconfigValue(String key) {
        try {
            return CacheApi.getDictSysConfig(key);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return e.getMessage();
        }
    }

    //强制终止流程
    @RequestMapping(value = "/event/force/terminate", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> forceTerminateProcess(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(engineEventRequests)) {
            engineEventRequests.forEach(engineEventRequest -> {
                sessionIds.add(engineEventRequest.getSessionId());
            });
        }
        String result = gwRequestService.forceCallbacked(sessionIds);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }


    @RequestMapping(value = "/callback/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "Gateway已回调流程分页列表", notes = "Gateway已回调流程分页列表", response = GwRequest.class, responseContainer = "page")
    public ResponseEntity<?> gwCallbackPageList(@RequestBody JSONObject params) {
        Page<GwRequestVo> pageResult = gwRequestService.getCallbackedGwRequestPageList(params);
        return buildSuccessResponse(pageResult);
    }


    @RequestMapping(value = "/engine/event/callbackUpStreamRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新回调上游", notes = "重新回调上游", response = ResponseEntity.class)
    public ResponseEntity<?> callbackRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {
        if (CollectionUtils.isEmpty(engineEventRequests)) {
            return buildSuccessResponse("success gatewayRetry");
        }
        List<String> sessionIdList =
                engineEventRequests.stream().map(EngineEventRequest::getSessionId).collect(Collectors.toList());
        gwRequestService.retryCallbackGw(sessionIdList);
        return buildSuccessResponse("success gatewayRetry");
    }

    @RequestMapping(value = "/engine/event/forceRejectRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新强制拒绝", notes = "重新强制拒绝", response = ResponseEntity.class)
    public ResponseEntity<?> forceRejectRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {

        if (CollectionUtils.isNotEmpty(engineEventRequests)) {
            List<String> sessionIdList = new ArrayList<>();
            engineEventRequests.forEach(re -> {
                sessionIdList.add(re.getSessionId());
            });
            String result = gwRequestService.forceRejectRetry(sessionIdList);
            return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
        }
        return buildSuccessResponse("success force reject!");
    }

    @RequestMapping(value = "/delete/cache", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "Gateway卡单流程分页列表", notes = "Gateway卡单流程分页列表", response = GwRequest.class, responseContainer = "page")
    public ResponseEntity<?> deleteCache(@RequestParam long startTime, @RequestParam long endTime) {
        singlePool.execute(() -> {
            redisDelTmpCacheService.delTmpCache(startTime, endTime);
        });
        return buildSuccessResponse("deleteCache data started!");
    }

    //强制拒绝
    @RequestMapping(value = "/event/force/reject", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> forceRejectProcess(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(engineEventRequests)) {
            engineEventRequests.forEach(engineEventRequest -> {
                sessionIds.add(engineEventRequest.getSessionId());
            });
        }
        String result = gwRequestService.forceReject(sessionIds);

        // 异步调用forceRejectSync 同步各环境
        singlePool.execute(() -> {
            LoggerProxy.info(LOGGER, "forceRejectSync, request:{}", JSON.toJSONString(engineEventRequests));
            gwRequestService.forceRejectSync(engineEventRequests);
        });

        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    //获取可以强制拒绝的事件
    @RequestMapping(value = "/canForceRejectEvents", method = RequestMethod.GET)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> canForceRejectEvents() {
        List<String> list=gwRequestService.getCanForceRejectEvents();
        return buildSuccessResponse(list);
    }


    @Autowired
    private BlockHandlerServiceHelp blockHandlerServiceHelp;

    //卡单企微操作
    @RequestMapping(value = "/qw", method = RequestMethod.GET)
    @ResponseBody
    @SystemLog
    public String qwHandler(String type, String... params) {
        try {
            blockHandlerServiceHelp.getBlockHandlerServiceByType(type).handler(params);
        } catch (Exception e) {
            LoggerProxy.error("卡单企微操作异常", LOGGER, "操作类型={}, params={}, 异常信息=",
                    type, params, e);
            return "failed,err msg= " + StringUtils.printStackTraceToString(e);
        }

        return "success";
    }

    @Autowired
    private WeChatHandlerServiceHelp weChatHandlerServiceHelp;

    /**
     * 处理企业微信消息
     * @param type 消息类型
     * @param params 参数
     * @return 处理结果
     */
    @RequestMapping(value = "/qw_ext", method = RequestMethod.GET)
    @ResponseBody
    public String handleQwExt(@RequestParam String type, @RequestParam(required = false) String params) {
        try {
            // 将 params 转换为 Map<String, String>
            Map<String, String> paramsMap = StringUtils.isNotBlank(params)
                    ? JSONObject.parseObject(params, Map.class)
                    : Collections.emptyMap();

            String requestUrl = String.format("%s?type=%s&params=%s", gatewayUrl, type, params);
            LoggerProxy.info("企业微信消息处理", LOGGER, "请求链接={}", requestUrl);

            weChatHandlerServiceHelp.getHandlerServiceByType(type).handler(paramsMap);
        } catch (Exception e) {
            LoggerProxy.error("企业微信消息处理异常", LOGGER, "消息类型={}, params={}, 异常信息=",
                    type, params, e);
            return "failed,err msg= " + StringUtils.printStackTraceToString(e);
        }

        return "success";
    }

    /**
     * 统一的策略订阅处理接口
     * 通过type参数区分不同的功能
     *
     * @param type 操作类型
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @param eventCode 事件代码
     * @param monitorId 监控ID
     * @return 处理结果
     */
    @RequestMapping(value = "/strategy-subscription", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "策略订阅统一处理接口", notes = "通过type参数区分不同功能")
    public ResponseEntity<?> handleStrategySubscription(
            @ApiParam(value = "操作类型", required = true) @RequestParam String type,
            @ApiParam(value = "用户ID") @RequestParam(required = false) String userId,
            @ApiParam(value = "策略类型") @RequestParam(required = false) String strategyType,
            @ApiParam(value = "事件代码") @RequestParam(required = false) String eventCode,
            @ApiParam(value = "监控ID") @RequestParam(required = false) String monitorId) {

        try {
            LoggerProxy.info("handleStrategySubscription", LOGGER,
                    "接收到策略订阅请求, type={}, userId={}, strategyType={}, eventCode={}, monitorId={}",
                    type, userId, strategyType, eventCode, monitorId);

            switch (type) {
                case "create":
                    return handleCreateSubscription(userId, strategyType);
                case "list":
                    return handleGetUserSubscriptions(userId, strategyType);
                case "pending":
                    return handleGetPendingSubscriptions();
                case "manual-push":
                    return handleManualPush();
                case "refresh-links":
                    return handleRefreshMonitorLinks(userId, strategyType);
                case "refresh-link-by-event":
                    return handleRefreshLinkByEventCode(eventCode);
                case "strategy-config":
                    return handleGetStrategyConfig(strategyType);
                case "all-strategy-configs":
                    return handleGetAllStrategyConfigs();
                case "test-link-generation":
                    return handleTestUnifiedLinkGeneration(strategyType, monitorId);
                default:
                    return buildErrorResponse("不支持的操作类型: " + type);
            }

        } catch (Exception e) {
            LoggerProxy.error("handleStrategySubscription", LOGGER,
                    "策略订阅处理异常, type=" + type + ", userId=" + userId + ", strategyType=" + strategyType, e);
            return buildErrorResponse("处理异常: " + e.getMessage());
        }
    }

    /**
     * 处理创建订阅请求
     */
    private ResponseEntity<?> handleCreateSubscription(String userId, String strategyType) {
        try {
            LoggerProxy.info("handleCreateSubscription", LOGGER,
                    "处理创建订阅请求, userId={}, strategyType={}", userId, strategyType);

            // 参数验证
            if (!SubscriptionUtils.validateSubscriptionParams(userId, strategyType)) {
                return buildErrorResponse("参数不能为空");
            }

            // 检查策略类型是否已配置
            if (!strategyMonitorConfigService.isStrategyTypeConfigured(strategyType)) {
                return buildErrorResponse("策略类型未配置监控项: " + strategyType);
            }

            // 创建订阅
            boolean success = strategySubscriptionService.createStrategySubscription(userId, strategyType);

            if (success) {
                LoggerProxy.info("handleCreateSubscription", LOGGER,
                        "创建订阅成功, userId={}, strategyType={}", userId, strategyType);
                return buildSuccessResponse(true);
            } else {
                LoggerProxy.warn("handleCreateSubscription", LOGGER,
                        "创建订阅失败, userId={}, strategyType={}", userId, strategyType);
                return buildErrorResponse("创建订阅失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("handleCreateSubscription", LOGGER,
                    "创建订阅异常, userId=" + userId + ", strategyType=" + strategyType, e);
            return buildErrorResponse("创建订阅异常: " + e.getMessage());
        }
    }

    /**
     * 处理查询用户订阅记录请求
     */
    private ResponseEntity<?> handleGetUserSubscriptions(String userId, String strategyType) {
        try {
            LoggerProxy.info("handleGetUserSubscriptions", LOGGER,
                    "查询用户订阅记录, userId={}, strategyType={}", userId, strategyType);

            if (userId == null || userId.trim().isEmpty()) {
                return buildErrorResponse("用户ID不能为空");
            }

            List<SubscriptionRecord> subscriptions = strategySubscriptionService.getUserSubscriptions(userId, strategyType);

            LoggerProxy.info("handleGetUserSubscriptions", LOGGER,
                    "查询用户订阅记录完成, userId={}, strategyType={}, count={}",
                    userId, strategyType, subscriptions.size());

            return buildSuccessResponse(subscriptions);

        } catch (Exception e) {
            LoggerProxy.error("handleGetUserSubscriptions", LOGGER,
                    "查询用户订阅记录异常, userId=" + userId + ", strategyType=" + strategyType, e);
            return buildErrorResponse("查询订阅记录异常: " + e.getMessage());
        }
    }

    /**
     * 处理查询待推送订阅记录请求
     */
    private ResponseEntity<?> handleGetPendingSubscriptions() {
        try {
            LoggerProxy.info("handleGetPendingSubscriptions", LOGGER, "查询待推送订阅记录");

            List<SubscriptionRecord> pendingSubscriptions = strategySubscriptionService.getPendingSubscriptions();

            LoggerProxy.info("handleGetPendingSubscriptions", LOGGER,
                    "查询待推送订阅记录完成, count={}", pendingSubscriptions.size());

            return buildSuccessResponse(pendingSubscriptions);

        } catch (Exception e) {
            LoggerProxy.error("handleGetPendingSubscriptions", LOGGER, "查询待推送订阅记录异常", e);
            return buildErrorResponse("查询待推送订阅记录异常: " + e.getMessage());
        }
    }

    /**
     * 处理手动触发推送请求
     */
    private ResponseEntity<?> handleManualPush() {
        try {
            LoggerProxy.info("handleManualPush", LOGGER, "手动触发推送");

            // 获取待推送记录
            List<SubscriptionRecord> pendingSubscriptions = strategySubscriptionService.getPendingSubscriptions();

            if (pendingSubscriptions.isEmpty()) {
                return buildSuccessResponse("没有需要推送的记录");
            }

            // 这里可以调用定时任务的逻辑
            String result = String.format("找到 %d 条待推送记录，请查看日志了解详细执行情况", pendingSubscriptions.size());

            LoggerProxy.info("handleManualPush", LOGGER, "手动触发推送完成, pendingCount={}", pendingSubscriptions.size());

            return buildSuccessResponse(result);

        } catch (Exception e) {
            LoggerProxy.error("handleManualPush", LOGGER, "手动触发推送异常", e);
            return buildErrorResponse("手动触发推送异常: " + e.getMessage());
        }
    }

    /**
     * 处理刷新监控链接请求
     */
    private ResponseEntity<?> handleRefreshMonitorLinks(String userId, String strategyType) {
        try {
            LoggerProxy.info("handleRefreshMonitorLinks", LOGGER,
                    "接收到刷新监控链接请求, userId={}, strategyType={}", userId, strategyType);

            if (StringUtils.isBlank(userId)) {
                return buildErrorResponse("用户ID不能为空");
            }

            Map<String, String> refreshedLinks = new HashMap<>();

            if (StringUtils.isNotBlank(strategyType)) {
                // 刷新指定策略类型的链接
                refreshedLinks = refreshLinksForStrategy(userId, strategyType);
            } else {
                // 刷新用户所有订阅的链接
                refreshedLinks = refreshAllLinksForUser(userId);
            }

            // 发送刷新后的链接给用户
            boolean sent = wechatMessageService.sendRefreshedMonitorLinks(userId, refreshedLinks);

            if (sent) {
                LoggerProxy.info("handleRefreshMonitorLinks", LOGGER,
                        "刷新监控链接成功, userId={}, strategyType={}, linkCount={}",
                        userId, strategyType, refreshedLinks.size());
                return buildSuccessResponse(refreshedLinks);
            } else {
                LoggerProxy.warn("handleRefreshMonitorLinks", LOGGER,
                        "发送刷新链接消息失败, userId={}, strategyType={}", userId, strategyType);
                return buildErrorResponse("发送刷新链接消息失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("handleRefreshMonitorLinks", LOGGER,
                    "刷新监控链接异常, userId=" + userId + ", strategyType=" + strategyType, e);
            return buildErrorResponse("刷新监控链接异常: " + e.getMessage());
        }
    }

    /**
     * 处理根据事件代码刷新链接请求
     */
    private ResponseEntity<?> handleRefreshLinkByEventCode(String eventCode) {
        try {
            LoggerProxy.info("handleRefreshLinkByEventCode", LOGGER, "刷新监控链接, eventCode={}", eventCode);

            if (StringUtils.isBlank(eventCode)) {
                return buildErrorResponse("事件代码不能为空");
            }

            String refreshedLink = strategyMonitorConfigService.refreshMonitorLink(eventCode);

            if (StringUtils.isNotBlank(refreshedLink)) {
                LoggerProxy.info("handleRefreshLinkByEventCode", LOGGER,
                        "刷新监控链接成功, eventCode={}, link={}", eventCode, refreshedLink);
                return buildSuccessResponse(refreshedLink);
            } else {
                LoggerProxy.warn("handleRefreshLinkByEventCode", LOGGER,
                        "刷新监控链接失败, eventCode={}", eventCode);
                return buildErrorResponse("刷新监控链接失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("handleRefreshLinkByEventCode", LOGGER,
                    "刷新监控链接异常, eventCode=" + eventCode, e);
            return buildErrorResponse("刷新监控链接异常: " + e.getMessage());
        }
    }

    /**
     * 处理获取策略配置请求
     */
    private ResponseEntity<?> handleGetStrategyConfig(String strategyType) {
        try {
            LoggerProxy.info("handleGetStrategyConfig", LOGGER, "获取策略配置信息, strategyType={}", strategyType);

            if (StringUtils.isBlank(strategyType)) {
                return buildErrorResponse("策略类型不能为空");
            }

            StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
            if (mapping == null) {
                return buildErrorResponse("策略类型未配置: " + strategyType);
            }

            Map<String, Object> configInfo = new HashMap<>();

            // 策略级别配置
            int defaultFrequency = 60;
            int defaultTotalPushes = 3;
            int strategyFrequency = mapping.getEffectiveFrequencyMinutes(defaultFrequency);
            int strategyTotalPushes = mapping.getEffectiveTotalPushes(defaultTotalPushes);

            configInfo.put("strategyName", mapping.getName());
            configInfo.put("strategyFrequencyMinutes", strategyFrequency);
            configInfo.put("strategyTotalPushes", strategyTotalPushes);
            configInfo.put("strategyConfigSummary", SubscriptionUtils.buildPushConfigSummary(strategyFrequency, strategyTotalPushes));

            // 监控项级别配置
            List<Map<String, Object>> monitorConfigs = new ArrayList<>();
            if (mapping.getMonitors() != null) {
                for (MonitorConfig monitor : mapping.getMonitors()) {
                    Map<String, Object> monitorInfo = new HashMap<>();
                    monitorInfo.put("id", monitor.getId());
                    monitorInfo.put("name", monitor.getName());
                    monitorInfo.put("eventCode", monitor.getEventCode());
                    monitorInfo.put("useDynamicLink", monitor.getUseDynamicLink());

                    int monitorFrequency = monitor.getEffectiveFrequencyMinutes(strategyFrequency);
                    int monitorTotalPushes = monitor.getEffectiveTotalPushes(strategyTotalPushes);

                    monitorInfo.put("frequencyMinutes", monitorFrequency);
                    monitorInfo.put("totalPushes", monitorTotalPushes);
                    monitorInfo.put("configSummary", SubscriptionUtils.buildPushConfigSummary(monitorFrequency, monitorTotalPushes));

                    monitorConfigs.add(monitorInfo);
                }
            }
            configInfo.put("monitors", monitorConfigs);

            LoggerProxy.info("handleGetStrategyConfig", LOGGER,
                    "获取策略配置信息完成, strategyType={}, monitorCount={}", strategyType, monitorConfigs.size());

            return buildSuccessResponse(configInfo);

        } catch (Exception e) {
            LoggerProxy.error("handleGetStrategyConfig", LOGGER,
                    "获取策略配置信息异常, strategyType=" + strategyType, e);
            return buildErrorResponse("获取策略配置信息异常: " + e.getMessage());
        }
    }

    /**
     * 处理获取所有策略配置请求
     */
    private ResponseEntity<?> handleGetAllStrategyConfigs() {
        try {
            LoggerProxy.info("handleGetAllStrategyConfigs", LOGGER, "获取所有策略配置信息");

            Map<String, StrategyMonitorMapping> allMappings = strategyMonitorConfigService.getAllStrategyMonitorMappings();

            List<Map<String, Object>> strategyConfigs = new ArrayList<>();

            // 默认配置
            int defaultFrequency = 60;
            int defaultTotalPushes = 3;

            for (Map.Entry<String, StrategyMonitorMapping> entry : allMappings.entrySet()) {
                String strategyType = entry.getKey();
                StrategyMonitorMapping mapping = entry.getValue();

                Map<String, Object> strategyInfo = new HashMap<>();
                strategyInfo.put("strategyType", strategyType);
                strategyInfo.put("strategyName", mapping.getName());

                int strategyFrequency = mapping.getEffectiveFrequencyMinutes(defaultFrequency);
                int strategyTotalPushes = mapping.getEffectiveTotalPushes(defaultTotalPushes);

                strategyInfo.put("frequencyMinutes", strategyFrequency);
                strategyInfo.put("totalPushes", strategyTotalPushes);
                strategyInfo.put("configSummary", SubscriptionUtils.buildPushConfigSummary(strategyFrequency, strategyTotalPushes));
                strategyInfo.put("monitorCount", mapping.getMonitors() != null ? mapping.getMonitors().size() : 0);

                // 统计自定义配置的监控项数量
                long customConfigCount = 0;
                if (mapping.getMonitors() != null) {
                    customConfigCount = mapping.getMonitors().stream()
                            .mapToLong(monitor -> {
                                int monitorFreq = monitor.getEffectiveFrequencyMinutes(strategyFrequency);
                                int monitorPushes = monitor.getEffectiveTotalPushes(strategyTotalPushes);
                                return (monitorFreq != strategyFrequency || monitorPushes != strategyTotalPushes) ? 1 : 0;
                            })
                            .sum();
                }
                strategyInfo.put("customConfigCount", customConfigCount);

                strategyConfigs.add(strategyInfo);
            }

            LoggerProxy.info("handleGetAllStrategyConfigs", LOGGER,
                    "获取所有策略配置信息完成, strategyCount={}", strategyConfigs.size());

            return buildSuccessResponse(strategyConfigs);

        } catch (Exception e) {
            LoggerProxy.error("handleGetAllStrategyConfigs", LOGGER, "获取所有策略配置信息异常", e);
            return buildErrorResponse("获取所有策略配置信息异常: " + e.getMessage());
        }
    }

    /**
     * 处理测试统一配置链接生成请求
     */
    private ResponseEntity<?> handleTestUnifiedLinkGeneration(String strategyType, String monitorId) {
        try {
            LoggerProxy.info("handleTestUnifiedLinkGeneration", LOGGER,
                    "测试统一配置链接生成, strategyType={}, monitorId={}", strategyType, monitorId);

            if (StringUtils.isBlank(strategyType) || StringUtils.isBlank(monitorId)) {
                return buildErrorResponse("策略类型和监控ID不能为空");
            }

            StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
            if (mapping == null) {
                return buildErrorResponse("策略类型未配置: " + strategyType);
            }

            MonitorConfig monitor = null;
            if (mapping.getMonitors() != null) {
                monitor = mapping.getMonitors().stream()
                        .filter(m -> monitorId.equals(m.getId()))
                        .findFirst()
                        .orElse(null);
            }

            if (monitor == null) {
                return buildErrorResponse("监控配置未找到: " + monitorId);
            }

            // 生成动态链接
            String dynamicLink = strategyMonitorConfigService.generateMonitorLink(monitor);

            if (StringUtils.isNotBlank(dynamicLink)) {
                LoggerProxy.info("handleTestUnifiedLinkGeneration", LOGGER,
                        "统一配置链接生成成功, strategyType={}, monitorId={}, link={}",
                        strategyType, monitorId, dynamicLink);
                return buildSuccessResponse(dynamicLink);
            } else {
                LoggerProxy.warn("handleTestUnifiedLinkGeneration", LOGGER,
                        "统一配置链接生成失败, strategyType={}, monitorId={}", strategyType, monitorId);
                return buildErrorResponse("链接生成失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("handleTestUnifiedLinkGeneration", LOGGER,
                    "测试统一配置链接生成异常, strategyType=" + strategyType + ", monitorId=" + monitorId, e);
            return buildErrorResponse("测试链接生成异常: " + e.getMessage());
        }
    }

    /**
     * 刷新指定策略类型的监控链接
     */
    private Map<String, String> refreshLinksForStrategy(String userId, String strategyType) {
        Map<String, String> refreshedLinks = new HashMap<>();

        StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
        if (mapping != null && mapping.getMonitors() != null) {
            for (MonitorConfig monitor : mapping.getMonitors()) {
                String refreshedLink = strategyMonitorConfigService.generateMonitorLink(monitor);
                refreshedLinks.put(monitor.getName(), refreshedLink);
            }
        }

        return refreshedLinks;
    }

    /**
     * 刷新用户所有订阅的监控链接
     */
    private Map<String, String> refreshAllLinksForUser(String userId) {
        Map<String, String> refreshedLinks = new HashMap<>();

        // 获取用户所有订阅记录
        List<SubscriptionRecord> subscriptions = strategySubscriptionService.getUserSubscriptions(userId, null);

        // 按策略类型分组
        Map<String, List<SubscriptionRecord>> strategyGroups = subscriptions.stream()
                .collect(Collectors.groupingBy(SubscriptionRecord::getStrategyType));

        for (String strategyType : strategyGroups.keySet()) {
            Map<String, String> strategyLinks = refreshLinksForStrategy(userId, strategyType);
            refreshedLinks.putAll(strategyLinks);
        }

        return refreshedLinks;
    }
}


