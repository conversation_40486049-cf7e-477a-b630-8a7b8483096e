package com.youxin.risk.admin.aspect;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.client.VariableCenterClient;
import com.youxin.risk.admin.dao.admin.AdminDataSourceConfigMapper;
import com.youxin.risk.commons.model.DatasourceConfigDO;
import com.youxin.risk.commons.model.DatasourceConfigNewDO;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class AdminDatasourceConfigAspect {
    private static final String INTERCEPT_METHOD_INSERT = "insert";
    private static final String INTERCEPT_METHOD_UPDATE = "update";

    private AdminDataSourceConfigMapper adminDataSourceConfigMapper;
    private VariableCenterClient variableCenterClient;

    // 定义切入点表达式，拦截AdminDataSourceMapper的insert、update操作
    @Pointcut("execution(* com.youxin.risk.admin.dao.admin.AdminDataSourceMapper.insert(..)) || " +
            "execution(* com.youxin.risk.admin.dao.admin.AdminDataSourceMapper.update(..))")
    public void mybatisOperation() {

    }

    @Around("mybatisOperation()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行SQL
        Object res = joinPoint.proceed();

        Object[] args = joinPoint.getArgs(); // 获取参数数组
        DatasourceConfigDO datasourceConfigDO = (DatasourceConfigDO) args[0];

        if (!"outer".equals(datasourceConfigDO.getDatasourceType())) {
            return res;
        }

        JSONObject variableTemplateJson = variableCenterClient.queryVariableTemplateByOldTemplateId(datasourceConfigDO.getVariableCenterId(),
                datasourceConfigDO.getDatasourceCode());
        if (variableTemplateJson == null) {
            return res;
        }

        String methodName = joinPoint.getSignature().getName();

        DatasourceConfigNewDO datasourceConfigNewDO = buildDatasourceConfigNew(datasourceConfigDO, variableTemplateJson);
        if (INTERCEPT_METHOD_INSERT.equals(methodName)) {
            adminDataSourceConfigMapper.insert(datasourceConfigNewDO);
        } else if (INTERCEPT_METHOD_UPDATE.equals(methodName)) {
            adminDataSourceConfigMapper.update(datasourceConfigNewDO);
        }

        return res;
    }

    private DatasourceConfigNewDO buildDatasourceConfigNew(DatasourceConfigDO datasourceConfigDO, JSONObject variableTemplateJson) {
        DatasourceConfigNewDO datasourceConfigNewDO = new DatasourceConfigNewDO();
        // id保持一致
        datasourceConfigNewDO.setId(datasourceConfigDO.getId());
        datasourceConfigNewDO.setVariableCenterId(variableTemplateJson.getLong("id"));
        datasourceConfigNewDO.setDatasourceCode(datasourceConfigDO.getDatasourceCode());
        datasourceConfigNewDO.setDatasourceName("");
        datasourceConfigNewDO.setTemplateType(variableTemplateJson.getInteger("templateType"));
        datasourceConfigNewDO.setCacheType(datasourceConfigDO.getOnlyUseCache());
        datasourceConfigNewDO.setCacheDay(datasourceConfigDO.getCacheDay());
        datasourceConfigNewDO.setCacheDayExpression(StringUtils.isBlank(datasourceConfigDO.getSecondCacheExpression()) ? null :datasourceConfigDO.getSecondCacheExpression());
        datasourceConfigNewDO.setDivideExpression(StringUtils.isBlank(datasourceConfigDO.getDivideExpression()) ? null : datasourceConfigDO.getDivideExpression());
        datasourceConfigNewDO.setUpdateUser(datasourceConfigDO.getUpdateUser());

        return datasourceConfigNewDO;
    }

    @Autowired
    public void setAdminDataSourceConfigMapper(AdminDataSourceConfigMapper adminDataSourceConfigMapper) {
        this.adminDataSourceConfigMapper = adminDataSourceConfigMapper;
    }

    @Autowired
    public void setVariableCenterClient(VariableCenterClient variableCenterClient) {
        this.variableCenterClient = variableCenterClient;
    }
}
