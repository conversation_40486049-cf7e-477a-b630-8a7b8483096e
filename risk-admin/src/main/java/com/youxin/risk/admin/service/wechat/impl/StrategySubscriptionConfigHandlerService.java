package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.utils.SubscriptionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 获取策略配置信息处理服务
 */
@Service
public class StrategySubscriptionConfigHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            String strategyType = getRequiredParam(params, "strategyType");

            LoggerProxy.info("processMessage", logger, "获取策略配置信息, strategyType={}", strategyType);

            StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
            if (mapping == null) {
                throw new IllegalArgumentException("策略类型未配置: " + strategyType);
            }

            Map<String, Object> configInfo = new HashMap<>();

            // 策略级别配置
            int defaultFrequency = 60;
            int defaultTotalPushes = 3;
            int strategyFrequency = mapping.getEffectiveFrequencyMinutes(defaultFrequency);
            int strategyTotalPushes = mapping.getEffectiveTotalPushes(defaultTotalPushes);

            configInfo.put("strategyName", mapping.getName());
            configInfo.put("strategyFrequencyMinutes", strategyFrequency);
            configInfo.put("strategyTotalPushes", strategyTotalPushes);
            configInfo.put("strategyConfigSummary", SubscriptionUtils.buildPushConfigSummary(strategyFrequency, strategyTotalPushes));

            // 监控项级别配置
            List<Map<String, Object>> monitorConfigs = new ArrayList<>();
            if (mapping.getMonitors() != null) {
                for (MonitorConfig monitor : mapping.getMonitors()) {
                    Map<String, Object> monitorInfo = new HashMap<>();
                    monitorInfo.put("id", monitor.getId());
                    monitorInfo.put("name", monitor.getName());
                    monitorInfo.put("eventCode", monitor.getEventCode());
                    monitorInfo.put("useDynamicLink", monitor.getUseDynamicLink());

                    int monitorFrequency = monitor.getEffectiveFrequencyMinutes(strategyFrequency);
                    int monitorTotalPushes = monitor.getEffectiveTotalPushes(strategyTotalPushes);

                    monitorInfo.put("frequencyMinutes", monitorFrequency);
                    monitorInfo.put("totalPushes", monitorTotalPushes);
                    monitorInfo.put("configSummary", SubscriptionUtils.buildPushConfigSummary(monitorFrequency, monitorTotalPushes));

                    monitorConfigs.add(monitorInfo);
                }
            }
            configInfo.put("monitors", monitorConfigs);

            LoggerProxy.info("processMessage", logger,
                    "获取策略配置信息完成, strategyType={}, monitorCount={}", strategyType, monitorConfigs.size());

            return configInfo;

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger,
                    "获取策略配置信息异常, params=" + params, e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 策略配置信息\n\n");
        
        String strategyType = getParam(params, "strategyType");
        markdownContent.append("> **策略类型:** ").append(infoMessage(strategyType)).append("\n");
        
        if (result instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> configInfo = (Map<String, Object>) result;
            
            markdownContent.append("> **策略名称:** ").append(infoMessage((String) configInfo.get("strategyName"))).append("\n");
            markdownContent.append("> **推送频率:** ").append(infoMessage(configInfo.get("strategyFrequencyMinutes") + " 分钟")).append("\n");
            markdownContent.append("> **总推送次数:** ").append(infoMessage(String.valueOf(configInfo.get("strategyTotalPushes")))).append("\n");
            markdownContent.append("> **配置摘要:** ").append(infoMessage((String) configInfo.get("strategyConfigSummary"))).append("\n\n");
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> monitors = (List<Map<String, Object>>) configInfo.get("monitors");
            
            if (monitors != null && !monitors.isEmpty()) {
                markdownContent.append("**监控项配置详情:**\n");
                for (int i = 0; i < monitors.size(); i++) {
                    Map<String, Object> monitor = monitors.get(i);
                    markdownContent.append(String.format("%d. **%s**\n", i + 1, monitor.get("name")));
                    markdownContent.append(String.format("   - ID: %s\n", monitor.get("id")));
                    markdownContent.append(String.format("   - 事件代码: %s\n", monitor.get("eventCode")));
                    markdownContent.append(String.format("   - 推送频率: %s 分钟\n", monitor.get("frequencyMinutes")));
                    markdownContent.append(String.format("   - 总推送次数: %s 次\n", monitor.get("totalPushes")));
                    markdownContent.append(String.format("   - 动态链接: %s\n", 
                            Boolean.TRUE.equals(monitor.get("useDynamicLink")) ? "是" : "否"));
                    markdownContent.append(String.format("   - 配置摘要: %s\n", monitor.get("configSummary")));
                }
            } else {
                markdownContent.append("该策略暂无监控项配置。");
            }
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("获取配置失败")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_CONFIG.getCode();
    }
}
