package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 查询待推送订阅记录处理服务
 */
@Service
public class StrategySubscriptionPendingHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            LoggerProxy.info("processMessage", logger, "查询待推送订阅记录");

            List<SubscriptionRecord> pendingSubscriptions = strategySubscriptionService.getPendingSubscriptions();
            
            LoggerProxy.info("processMessage", logger, 
                    "查询待推送订阅记录完成, count={}", pendingSubscriptions.size());
            
            return pendingSubscriptions;

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger, "查询待推送订阅记录异常", e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 待推送订阅记录查询\n\n");
        
        if (result instanceof List) {
            @SuppressWarnings("unchecked")
            List<SubscriptionRecord> pendingSubscriptions = (List<SubscriptionRecord>) result;
            
            markdownContent.append("> **待推送数量:** ").append(infoMessage(String.valueOf(pendingSubscriptions.size()))).append("\n\n");
            
            if (pendingSubscriptions.isEmpty()) {
                markdownContent.append("暂无待推送的订阅记录。");
            } else {
                markdownContent.append("**待推送详情:**\n");
                for (int i = 0; i < pendingSubscriptions.size(); i++) {
                    SubscriptionRecord record = pendingSubscriptions.get(i);
                    markdownContent.append(String.format("%d. **用户:** %s\n", i + 1, record.getUserId()));
                    markdownContent.append(String.format("   - 策略类型: %s\n", record.getStrategyType()));
                    markdownContent.append(String.format("   - 监控ID: %s\n", record.getMonitorId()));
                    markdownContent.append(String.format("   - 已推送: %d/%d 次\n", 
                            record.getSentPushes(), record.getTotalPushes()));
                    markdownContent.append(String.format("   - 下次推送时间: %s\n", 
                            record.getNextPushTime() != null ? record.getNextPushTime().toString() : "未设置"));
                }
            }
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("查询异常")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_PENDING.getCode();
    }
}
