package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SubscriptionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 获取所有策略配置处理服务
 */
@Service
public class StrategySubscriptionAllConfigsHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            LoggerProxy.info("processMessage", logger, "获取所有策略配置信息");

            Map<String, StrategyMonitorMapping> allMappings = strategyMonitorConfigService.getAllStrategyMonitorMappings();

            List<Map<String, Object>> strategyConfigs = new ArrayList<>();

            // 默认配置
            int defaultFrequency = 60;
            int defaultTotalPushes = 3;

            for (Map.Entry<String, StrategyMonitorMapping> entry : allMappings.entrySet()) {
                String strategyType = entry.getKey();
                StrategyMonitorMapping mapping = entry.getValue();

                Map<String, Object> strategyInfo = new HashMap<>();
                strategyInfo.put("strategyType", strategyType);
                strategyInfo.put("strategyName", mapping.getName());

                int strategyFrequency = mapping.getEffectiveFrequencyMinutes(defaultFrequency);
                int strategyTotalPushes = mapping.getEffectiveTotalPushes(defaultTotalPushes);

                strategyInfo.put("frequencyMinutes", strategyFrequency);
                strategyInfo.put("totalPushes", strategyTotalPushes);
                strategyInfo.put("configSummary", SubscriptionUtils.buildPushConfigSummary(strategyFrequency, strategyTotalPushes));
                strategyInfo.put("monitorCount", mapping.getMonitors() != null ? mapping.getMonitors().size() : 0);

                // 统计自定义配置的监控项数量
                long customConfigCount = 0;
                if (mapping.getMonitors() != null) {
                    customConfigCount = mapping.getMonitors().stream()
                            .mapToLong(monitor -> {
                                int monitorFreq = monitor.getEffectiveFrequencyMinutes(strategyFrequency);
                                int monitorPushes = monitor.getEffectiveTotalPushes(strategyTotalPushes);
                                return (monitorFreq != strategyFrequency || monitorPushes != strategyTotalPushes) ? 1 : 0;
                            })
                            .sum();
                }
                strategyInfo.put("customConfigCount", customConfigCount);

                strategyConfigs.add(strategyInfo);
            }

            LoggerProxy.info("processMessage", logger,
                    "获取所有策略配置信息完成, strategyCount={}", strategyConfigs.size());

            return strategyConfigs;

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger, "获取所有策略配置信息异常", e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 所有策略配置信息\n\n");
        
        if (result instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> strategyConfigs = (List<Map<String, Object>>) result;
            
            markdownContent.append("> **策略总数:** ").append(infoMessage(String.valueOf(strategyConfigs.size()))).append("\n\n");
            
            if (strategyConfigs.isEmpty()) {
                markdownContent.append("暂无策略配置信息。");
            } else {
                markdownContent.append("**策略配置详情:**\n");
                for (int i = 0; i < strategyConfigs.size(); i++) {
                    Map<String, Object> strategy = strategyConfigs.get(i);
                    markdownContent.append(String.format("%d. **%s** (%s)\n", 
                            i + 1, strategy.get("strategyName"), strategy.get("strategyType")));
                    markdownContent.append(String.format("   - 推送频率: %s 分钟\n", strategy.get("frequencyMinutes")));
                    markdownContent.append(String.format("   - 总推送次数: %s 次\n", strategy.get("totalPushes")));
                    markdownContent.append(String.format("   - 配置摘要: %s\n", strategy.get("configSummary")));
                    markdownContent.append(String.format("   - 监控项数量: %s 个\n", strategy.get("monitorCount")));
                    markdownContent.append(String.format("   - 自定义配置: %s 个\n", strategy.get("customConfigCount")));
                }
                
                // 添加统计信息
                int totalMonitors = strategyConfigs.stream()
                        .mapToInt(s -> (Integer) s.get("monitorCount"))
                        .sum();
                long totalCustomConfigs = strategyConfigs.stream()
                        .mapToLong(s -> (Long) s.get("customConfigCount"))
                        .sum();
                
                markdownContent.append("\n**统计信息:**\n");
                markdownContent.append(String.format("- 总监控项数量: %d 个\n", totalMonitors));
                markdownContent.append(String.format("- 自定义配置数量: %d 个\n", totalCustomConfigs));
            }
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("获取配置失败")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_ALL_CONFIGS.getCode();
    }
}
