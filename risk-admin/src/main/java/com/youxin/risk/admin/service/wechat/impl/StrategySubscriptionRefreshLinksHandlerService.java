package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 刷新监控链接处理服务
 */
@Service
public class StrategySubscriptionRefreshLinksHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            String userId = getRequiredParam(params, "userId");
            String strategyType = getParam(params, "strategyType");

            LoggerProxy.info("processMessage", logger,
                    "接收到刷新监控链接请求, userId={}, strategyType={}", userId, strategyType);

            Map<String, String> refreshedLinks = new HashMap<>();

            if (StringUtils.isNotBlank(strategyType)) {
                // 刷新指定策略类型的链接
                refreshedLinks = refreshLinksForStrategy(userId, strategyType);
            } else {
                // 刷新用户所有订阅的链接
                refreshedLinks = refreshAllLinksForUser(userId);
            }

            // 发送刷新后的链接给用户
            boolean sent = wechatMessageService.sendRefreshedMonitorLinks(userId, refreshedLinks);

            if (sent) {
                LoggerProxy.info("processMessage", logger,
                        "刷新监控链接成功, userId={}, strategyType={}, linkCount={}",
                        userId, strategyType, refreshedLinks.size());
                return refreshedLinks;
            } else {
                LoggerProxy.warn("processMessage", logger,
                        "发送刷新链接消息失败, userId={}, strategyType={}", userId, strategyType);
                throw new RuntimeException("发送刷新链接消息失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger,
                    "刷新监控链接异常, params=" + params, e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 刷新监控链接\n\n");
        
        String userId = getParam(params, "userId");
        String strategyType = getParam(params, "strategyType");
        
        markdownContent.append("> **用户ID:** ").append(infoMessage(userId)).append("\n");
        if (strategyType != null && !strategyType.trim().isEmpty()) {
            markdownContent.append("> **策略类型:** ").append(infoMessage(strategyType)).append("\n");
        }
        
        if (result instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, String> refreshedLinks = (Map<String, String>) result;
            
            markdownContent.append("> **刷新链接数量:** ").append(infoMessage(String.valueOf(refreshedLinks.size()))).append("\n");
            markdownContent.append("> **操作结果:** ").append(successMessage("刷新成功")).append("\n\n");
            
            if (!refreshedLinks.isEmpty()) {
                markdownContent.append("**刷新的监控链接:**\n");
                for (Map.Entry<String, String> entry : refreshedLinks.entrySet()) {
                    String monitorName = entry.getKey();
                    String link = entry.getValue();
                    if (StringUtils.isNotEmpty(link)) {
                        markdownContent.append(String.format("- [%s](%s)\n", monitorName, link));
                    } else {
                        markdownContent.append(String.format("- %s - 链接刷新失败\n", monitorName));
                    }
                }
                markdownContent.append("\n> 新链接已通过企业微信发送给您。");
            }
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("刷新异常")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_REFRESH_LINKS.getCode();
    }

    /**
     * 刷新指定策略类型的监控链接
     */
    private Map<String, String> refreshLinksForStrategy(String userId, String strategyType) {
        Map<String, String> refreshedLinks = new HashMap<>();

        StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
        if (mapping != null && mapping.getMonitors() != null) {
            for (MonitorConfig monitor : mapping.getMonitors()) {
                String refreshedLink = strategyMonitorConfigService.generateMonitorLink(monitor);
                refreshedLinks.put(monitor.getName(), refreshedLink);
            }
        }

        return refreshedLinks;
    }

    /**
     * 刷新用户所有订阅的监控链接
     */
    private Map<String, String> refreshAllLinksForUser(String userId) {
        Map<String, String> refreshedLinks = new HashMap<>();

        // 获取用户所有订阅记录
        List<SubscriptionRecord> subscriptions = strategySubscriptionService.getUserSubscriptions(userId, null);

        // 按策略类型分组
        Map<String, List<SubscriptionRecord>> strategyGroups = subscriptions.stream()
                .collect(Collectors.groupingBy(SubscriptionRecord::getStrategyType));

        for (String strategyType : strategyGroups.keySet()) {
            Map<String, String> strategyLinks = refreshLinksForStrategy(userId, strategyType);
            refreshedLinks.putAll(strategyLinks);
        }

        return refreshedLinks;
    }
}
