<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.admin.AdminDataSourceConfigMapper">
  <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.DatasourceConfigNewDO">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <id column="variable_center_id" property="variableCenterId" jdbcType="BIGINT"/>
    <result column="datasource_code" property="datasourceCode" jdbcType="VARCHAR"/>
    <result column="datasource_name" property="datasourceName" jdbcType="VARCHAR"/>
    <result column="template_type" property="templateType" jdbcType="TINYINT"/>
    <result column="cache_type" property="cacheType" jdbcType="TINYINT"/>
    <result column="cache_day" property="cacheDay" jdbcType="INTEGER"/>
    <result column="cache_day_expression" property="cacheDayExpression" jdbcType="VARCHAR"/>
    <result column="divide_expression" property="divideExpression" jdbcType="VARCHAR"/>
    <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, variable_center_id, datasource_code, datasource_name, template_type, cache_type, cache_day,
    cache_day_expression, divide_expression, update_user, update_time
  </sql>

  <insert id="insert" parameterType="com.youxin.risk.commons.model.DatasourceConfigNewDO">
    insert into admin_datasource_config_new (
      id,
      variable_center_id,
      datasource_code,
      datasource_name,
      template_type,
      cache_type,
      cache_day,
      cache_day_expression,
      divide_expression,
      update_user,
      update_time,
      create_time
    ) values ( #{id},
               #{variableCenterId},
               #{datasourceCode},
               #{datasourceName},
               #{templateType},
               #{cacheType},
               #{cacheDay},
               #{cacheDayExpression},
               #{divideExpression},
               #{updateUser},
               now(),
               now()
             )
  </insert>

  <update id="update" parameterType="com.youxin.risk.commons.model.DatasourceConfigNewDO">
    update admin_datasource_config_new
    <set>
      <if test="variableCenterId != null">
        variable_center_id = #{variableCenterId},
      </if>
      <if test="cacheType != null">
        cache_type = #{cacheType},
      </if>
      <if test="cacheDay != null">
        cache_day = #{cacheDay},
      </if>
      <if test="cacheDayExpression != null">
        cache_day_expression = #{cacheDayExpression},
      </if>
      <if test="divideExpression != null">
        divide_expression = #{divideExpression},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
    </set>
    where id = #{id}
  </update>
</mapper>