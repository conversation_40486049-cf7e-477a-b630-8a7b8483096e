# 策略订阅控制器迁移说明

## 迁移概述

本次迁移将 `StrategySubscriptionController` 的所有功能迁移到 `BlockApplicationController` 中，以确保企业微信应用回调具有适当的权限。

## 迁移详情

### 1. 新增统一处理接口

在 `BlockApplicationController` 中新增了统一的策略订阅处理接口：

```java
@RequestMapping(value = "/strategy-subscription", method = {RequestMethod.GET, RequestMethod.POST})
@ResponseBody
@ApiOperation(value = "策略订阅统一处理接口", notes = "通过type参数区分不同功能")
public ResponseEntity<?> handleStrategySubscription(
        @ApiParam(value = "操作类型", required = true) @RequestParam String type,
        @ApiParam(value = "用户ID") @RequestParam(required = false) String userId,
        @ApiParam(value = "策略类型") @RequestParam(required = false) String strategyType,
        @ApiParam(value = "事件代码") @RequestParam(required = false) String eventCode,
        @ApiParam(value = "监控ID") @RequestParam(required = false) String monitorId)
```

### 2. 支持的操作类型

通过 `type` 参数区分不同的功能：

| type 值 | 功能描述 | 原始端点 |
|---------|----------|----------|
| `create` | 创建策略监控订阅 | `/admin/strategy/subscription/create` |
| `list` | 查询用户订阅记录 | `/admin/strategy/subscription/list` |
| `pending` | 查询待推送的订阅记录 | `/admin/strategy/subscription/pending` |
| `manual-push` | 手动触发推送 | `/admin/strategy/subscription/manual-push` |
| `refresh-links` | 刷新监控链接 | `/admin/strategy/subscription/refresh-links` |
| `refresh-link-by-event` | 根据事件代码刷新链接 | `/admin/strategy/subscription/refresh-link-by-event` |
| `strategy-config` | 获取策略配置信息 | `/admin/strategy/subscription/strategy-config` |
| `all-strategy-configs` | 获取所有策略配置 | `/admin/strategy/subscription/all-strategy-configs` |
| `test-link-generation` | 测试统一配置链接生成 | `/admin/strategy/subscription/test-link-generation` |

### 3. 新的端点URL格式

所有原始的策略订阅端点现在都通过以下格式访问：

```
/block/strategy-subscription?type={操作类型}&{其他参数}
```

#### 示例：

- **创建订阅**：
  ```
  POST /block/strategy-subscription?type=create&userId=user123&strategyType=RISK_STRATEGY
  ```

- **查询用户订阅**：
  ```
  GET /block/strategy-subscription?type=list&userId=user123&strategyType=RISK_STRATEGY
  ```

- **刷新监控链接**：
  ```
  POST /block/strategy-subscription?type=refresh-links&userId=user123&strategyType=RISK_STRATEGY
  ```

### 4. WechatMessageService 更新

更新了 `WechatMessageService` 中的链接生成逻辑，将原来指向 `/admin/strategy/subscription/` 的链接更新为指向新的 `/block/strategy-subscription` 端点：

- **订阅成功通知中的刷新链接**：
  ```
  /block/strategy-subscription?type=refresh-links&userId={userId}&strategyType={strategyType}
  ```

- **定时推送中的刷新链接**：
  ```
  /block/strategy-subscription?type=refresh-links&userId={userId}
  ```

### 5. 保持的功能

- 所有原有的业务逻辑保持不变
- 参数验证逻辑保持不变
- 错误处理逻辑保持不变
- 日志记录格式保持不变

### 6. 依赖注入

在 `BlockApplicationController` 中新增了以下依赖注入：

```java
@Autowired
private StrategySubscriptionService strategySubscriptionService;

@Autowired
private StrategyMonitorConfigService strategyMonitorConfigService;

@Autowired
private WechatMessageService wechatMessageService;
```

## 迁移后的优势

1. **权限统一**：所有策略订阅相关的企业微信回调现在都通过具有适当权限的 `BlockApplicationController` 处理
2. **接口整合**：通过 `type` 参数实现了功能的统一管理
3. **向后兼容**：保持了所有原有的业务逻辑和参数格式
4. **易于维护**：集中管理减少了代码重复

## 注意事项

1. 原有的 `StrategySubscriptionController` 可以在确认迁移成功后删除
2. 需要更新相关的API文档和前端调用代码
3. 建议进行充分的测试以确保所有功能正常工作
