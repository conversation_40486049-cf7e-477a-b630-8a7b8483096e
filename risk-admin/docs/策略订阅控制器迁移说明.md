# 策略订阅控制器迁移说明

## 迁移概述

本次迁移将 `StrategySubscriptionController` 的所有功能重构为基于 Handler 模式的企业微信消息处理服务，通过现有的 `/block/qw_ext` 接口进行统一处理，以确保企业微信应用回调具有适当的权限。

## 迁移详情

### 1. Handler 模式重构

将原有的 Controller 端点方法重构为独立的 Handler 服务类，每个 Handler 继承 `AbstractWeChatHandlerService` 并实现特定的策略订阅功能。

#### 创建的 Handler 类：

1. **AbstractStrategySubscriptionHandlerService** - 策略订阅处理服务抽象基类
2. **StrategySubscriptionCreateHandlerService** - 创建策略监控订阅
3. **StrategySubscriptionListHandlerService** - 查询用户订阅记录
4. **StrategySubscriptionPendingHandlerService** - 查询待推送的订阅记录
5. **StrategySubscriptionManualPushHandlerService** - 手动触发推送
6. **StrategySubscriptionRefreshLinksHandlerService** - 刷新监控链接

### 2. 新增操作类型枚举

在 `AlertExtendOperationTypeEnum` 中新增了策略订阅相关的操作类型：

| 枚举值 | 功能描述 | Handler 类 |
|--------|----------|------------|
| `STRATEGY_SUBSCRIPTION_CREATE` | 创建策略监控订阅 | `StrategySubscriptionCreateHandlerService` |
| `STRATEGY_SUBSCRIPTION_LIST` | 查询用户订阅记录 | `StrategySubscriptionListHandlerService` |
| `STRATEGY_SUBSCRIPTION_PENDING` | 查询待推送的订阅记录 | `StrategySubscriptionPendingHandlerService` |
| `STRATEGY_SUBSCRIPTION_MANUAL_PUSH` | 手动触发推送 | `StrategySubscriptionManualPushHandlerService` |
| `STRATEGY_SUBSCRIPTION_REFRESH_LINKS` | 刷新监控链接 | `StrategySubscriptionRefreshLinksHandlerService` |
| `STRATEGY_SUBSCRIPTION_REFRESH_LINK_BY_EVENT` | 根据事件代码刷新链接 | (待实现) |
| `STRATEGY_SUBSCRIPTION_CONFIG` | 获取策略配置信息 | (待实现) |
| `STRATEGY_SUBSCRIPTION_ALL_CONFIGS` | 获取所有策略配置 | (待实现) |
| `STRATEGY_SUBSCRIPTION_TEST_LINK` | 测试统一配置链接生成 | (待实现) |

### 3. 新的调用方式

所有策略订阅功能现在通过现有的 `/block/qw_ext` 接口调用，使用 Handler 模式处理：

```
GET /block/qw_ext?type={handler_type}&params={json_params}
```

#### 调用示例：

- **创建订阅**：
  ```
  GET /block/qw_ext?type=strategy_subscription_create&params={"userId":"user123","strategyType":"RISK_STRATEGY"}
  ```

- **查询用户订阅**：
  ```
  GET /block/qw_ext?type=strategy_subscription_list&params={"userId":"user123","strategyType":"RISK_STRATEGY"}
  ```

- **刷新监控链接**：
  ```
  GET /block/qw_ext?type=strategy_subscription_refresh_links&params={"userId":"user123","strategyType":"RISK_STRATEGY"}
  ```

### 4. WechatMessageService 更新

更新了 `WechatMessageService` 中的链接生成逻辑，将原来指向 `/admin/strategy/subscription/` 的链接更新为使用新的 Handler 方式：

- **订阅成功通知中的刷新链接**：
  ```
  /block/qw_ext?type=strategy_subscription_refresh_links&params={"userId":"{userId}","strategyType":"{strategyType}"}
  ```

- **定时推送中的刷新链接**：
  ```
  /block/qw_ext?type=strategy_subscription_refresh_links&params={"userId":"{userId}"}
  ```

### 5. 保持的功能

- 所有原有的业务逻辑保持不变
- 参数验证逻辑保持不变
- 错误处理逻辑保持不变
- 日志记录格式保持不变

### 6. Handler 架构优势

采用 Handler 模式的优势：

1. **统一入口**：所有策略订阅功能通过 `/block/qw_ext` 统一处理
2. **权限继承**：自动继承 `BlockApplicationController` 的企业微信权限
3. **模块化设计**：每个功能独立成 Handler，便于维护和扩展
4. **消息格式统一**：所有 Handler 返回统一格式的 Markdown 消息
5. **自动注册**：Handler 通过 Spring 自动注册到 `WeChatHandlerServiceHelp`

## 迁移后的优势

1. **权限统一**：所有策略订阅相关的企业微信回调现在都通过具有适当权限的 `BlockApplicationController` 处理
2. **架构清晰**：采用 Handler 模式，每个功能职责单一，便于维护
3. **统一入口**：通过 `/block/qw_ext` 接口统一处理，减少接口数量
4. **消息格式统一**：所有 Handler 返回统一格式的 Markdown 消息
5. **易于扩展**：新增功能只需创建新的 Handler 类即可

## 已完成的工作

1. ✅ 删除了原有的 `StrategySubscriptionController` 类
2. ✅ 在 `AlertExtendOperationTypeEnum` 中新增了策略订阅相关的操作类型
3. ✅ 创建了 `AbstractStrategySubscriptionHandlerService` 抽象基类
4. ✅ 实现了 5 个核心功能的 Handler 类
5. ✅ 更新了 `WechatMessageService` 中的链接生成逻辑

## 待完成的工作

1. 🔄 实现剩余的 Handler 类（根据事件代码刷新链接、获取策略配置等）
2. 🔄 更新相关的API文档和前端调用代码
3. 🔄 进行充分的测试以确保所有功能正常工作

## 测试建议

建议按以下顺序测试各个功能：

1. 测试创建订阅功能
2. 测试查询用户订阅记录功能
3. 测试刷新监控链接功能
4. 测试查询待推送记录功能
5. 测试手动触发推送功能
