package com.youxin.risk.commons.constants;

/**
 * 告警扩展操作类型枚举
 */
public enum AlertExtendOperationTypeEnum {

    SLS_DASHBOARD_LINK("sls_dashboard_link", "SLS仪表盘链接"),

    // 策略订阅相关操作类型
    STRATEGY_SUBSCRIPTION_CREATE("strategy_subscription_create", "创建策略监控订阅"),
    STRATEGY_SUBSCRIPTION_LIST("strategy_subscription_list", "查询用户订阅记录"),
    STRATEGY_SUBSCRIPTION_PENDING("strategy_subscription_pending", "查询待推送的订阅记录"),
    STRATEGY_SUBSCRIPTION_MANUAL_PUSH("strategy_subscription_manual_push", "手动触发推送"),
    STRATEGY_SUBSCRIPTION_REFRESH_LINKS("strategy_subscription_refresh_links", "刷新监控链接"),
    STRATEGY_SUBSCRIPTION_REFRESH_LINK_BY_EVENT("strategy_subscription_refresh_link_by_event", "根据事件代码刷新链接"),
    STRATEGY_SUBSCRIPTION_CONFIG("strategy_subscription_config", "获取策略配置信息"),
    STRATEGY_SUBSCRIPTION_ALL_CONFIGS("strategy_subscription_all_configs", "获取所有策略配置"),
    STRATEGY_SUBSCRIPTION_TEST_LINK("strategy_subscription_test_link", "测试统一配置链接生成");
    
    private String code;
    private String desc;
    
    AlertExtendOperationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static AlertExtendOperationTypeEnum getByCode(String code) {
        for (AlertExtendOperationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}