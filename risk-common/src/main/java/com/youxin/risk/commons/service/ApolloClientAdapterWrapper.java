package com.youxin.risk.commons.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Apollo客户端适配器包装类
 * 用于在测试中模拟ApolloClientAdapter的静态方法
 *
 * <AUTHOR>
 * @create 2025/4/2 10:57
 * @desc 封装ApolloClientAdapter的静态方法，使其可以在测试中被模拟
 */
@Component
public class ApolloClientAdapterWrapper {

    /**
     * 获取Map类型的配置
     * 包装ApolloClientAdapter.getMapConfig方法
     *
     * @param namespace 命名空间
     * @param key 配置键
     * @param valueType 值类型
     * @param <T> 泛型类型
     * @return 配置值Map
     */
    public <T> Map<String, T> getMapConfig(ApolloNamespaceEnum namespace, String key, Class<T> valueType) {
        return ApolloClientAdapter.getMapConfig(namespace, key, valueType);
    }

    public <T> List<T> getListConfig(ApolloNamespaceEnum namespace, String key, Class<T> valueType) {
        return ApolloClientAdapter.getListConfig(namespace, key, valueType);
    }

    public String getStringConfig(ApolloNamespaceEnum namespace, String key, String defaultValue) {
        return ApolloClientAdapter.getStringConfig(namespace, key, defaultValue);
    }

    public Boolean getBooleanConfig(ApolloNamespaceEnum namespace, String key, Boolean defaultValue) {
        return ApolloClientAdapter.getBooleanConfig(namespace, key, defaultValue);
    }

}
