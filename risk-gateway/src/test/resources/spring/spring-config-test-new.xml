<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns="http://www.springframework.org/schema/beans"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:util="http://www.springframework.org/schema/util"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd">

	<import resource="classpath:spring/spring-config.xml"/>
	<import resource="classpath:spring/spring-transaction.xml"/>
	<!-- 覆盖原有 Bean 定义 -->
	<bean id="apolloClientAdapterWrapper" class="org.mockito.Mockito" factory-method="mock">
		<constructor-arg value="com.youxin.risk.commons.service.ApolloClientAdapterWrapper"/>
	</bean>

	<!-- 配置测试专用的 JdbcTemplate -->
	<bean id="testJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<constructor-arg ref="gwDataSource"/> <!-- 使用主数据源 -->
	</bean>

<!--	<bean id="eventMirrorServiceImpl" class="org.mockito.Mockito" factory-method="spy">-->
<!--&lt;!&ndash;		<constructor-arg value="com.youxin.risk.engine.service.task.mirror.impl.EventMirrorServiceImpl"/>&ndash;&gt;-->

<!--		<constructor-arg>-->
<!--			<bean class="com.youxin.risk.engine.service.task.mirror.impl.EventMirrorServiceImpl"/>-->
<!--		</constructor-arg>-->
<!--	</bean>-->

<!--	<bean id="strategyTypeMirrorServiceImpl" class="org.mockito.Mockito" factory-method="spy">-->
<!--		&lt;!&ndash;		<constructor-arg value="com.youxin.risk.engine.service.task.mirror.impl.EventMirrorServiceImpl"/>&ndash;&gt;-->

<!--		<constructor-arg>-->
<!--			<bean class="com.youxin.risk.engine.service.task.mirror.impl.StrategyTypeMirrorServiceImpl"/>-->
<!--		</constructor-arg>-->
<!--	</bean>-->
</beans>
