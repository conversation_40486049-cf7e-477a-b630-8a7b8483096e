<?xml version="1.0" encoding="UTF-8" ?>
<configuration>


     <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder>
    </appender>

    <logger name="org.apache.axis.ConfigurationException" level="INFO" />

    <!-- 方案1：打印SQL语句（不包含参数） -->
    <logger name="com.youxin.risk.gateway.mapper" level="DEBUG">
<!--        <appender-ref ref="console" />-->
    </logger>

<!--    &lt;!&ndash; 方案2：打印完整SQL（包含参数） &ndash;&gt;-->
<!--    <logger name="jdbc.sqlonly" level="DEBUG">-->
<!--        <appender-ref ref="console" />-->
<!--    </logger>-->
<!--    &lt;!&ndash; 方案3：最详细日志（包含结果集） &ndash;&gt;-->
<!--    <logger name="jdbc.sqltiming" level="DEBUG">-->
<!--        <appender-ref ref="console" />-->
<!--    </logger>-->
    <root level="INFO">
        <appender-ref ref="console" />
    </root>
</configuration>
