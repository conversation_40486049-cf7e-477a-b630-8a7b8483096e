package com.youxin.risk.gateway.utils;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.model.Event;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * EventLogConfigUtil测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class EventLogConfigUtilTest {

    private Event testEvent;

    @Before
    public void setUp() {
        testEvent = new Event();
        testEvent.setSessionId("test-session-123");
        testEvent.set("eventCode", "ApiVerify");
        testEvent.set("userKey", "user123");
        testEvent.set("sourceSystem", "testSystem");
        testEvent.set("occurTime", "2024-01-15 10:30:45");

        // 设置验证结果
        Map<String, Object> verifyResult = new HashMap<>();
        verifyResult.put("riskScore", 85);
        verifyResult.put("riskLevel", "MEDIUM");
        verifyResult.put("finalDecision", "APPROVE");
        testEvent.setVerifyResult(verifyResult);
    }

    @Test
    public void testExtractEventData_WithValidConfig() {
        // 模拟Nacos配置
        String mockConfig = "{\n" +
                "  \"ApiVerify\": {\n" +
                "    \"fields\": [\n" +
                "      {\n" +
                "        \"name\": \"sessionId\",\n" +
                "        \"expression\": \"$.params.sessionId\",\n" +
                "        \"type\": \"jsonpath\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"name\": \"userKey\",\n" +
                "        \"expression\": \"$.params.userKey\",\n" +
                "        \"type\": \"jsonpath\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"targetKey\": \"eventInfo\"\n" +
                "  }\n" +
                "}";

        try (MockedStatic<com.youxin.apollo.client.NacosClient> mockedNacosClient = 
             Mockito.mockStatic(com.youxin.apollo.client.NacosClient.class)) {
            
            mockedNacosClient.when(() -> 
                com.youxin.apollo.client.NacosClient.getByNameSpace(anyString(), eq("event.log.config"), anyString()))
                .thenReturn(mockConfig);

            String result = EventLogConfigUtil.extractEventData(testEvent);
            
            assertNotNull(result);
            assertFalse(result.isEmpty());
            
            // 验证JSON格式
            Map<String, Object> resultMap = JSON.parseObject(result, Map.class);
            assertTrue(resultMap.containsKey("eventInfo"));
            
            Map<String, Object> eventInfo = (Map<String, Object>) resultMap.get("eventInfo");
            assertEquals("test-session-123", eventInfo.get("sessionId"));
            assertEquals("user123", eventInfo.get("userKey"));
        }
    }

    @Test
    public void testExtractEventData_WithMvelExpression() {
        // 模拟包含MVEL表达式的配置
        String mockConfig = "{\n" +
                "  \"ApiVerify\": {\n" +
                "    \"fields\": [\n" +
                "      {\n" +
                "        \"name\": \"userInfo\",\n" +
                "        \"expression\": \"params.userKey + '_' + params.sourceSystem\",\n" +
                "        \"type\": \"mvel\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"name\": \"riskScore\",\n" +
                "        \"expression\": \"verifyResult.riskScore\",\n" +
                "        \"type\": \"mvel\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";

        try (MockedStatic<com.youxin.apollo.client.NacosClient> mockedNacosClient = 
             Mockito.mockStatic(com.youxin.apollo.client.NacosClient.class)) {
            
            mockedNacosClient.when(() -> 
                com.youxin.apollo.client.NacosClient.getByNameSpace(anyString(), eq("event.log.config"), anyString()))
                .thenReturn(mockConfig);

            String result = EventLogConfigUtil.extractEventData(testEvent);
            
            assertNotNull(result);
            assertFalse(result.isEmpty());
            
            // 验证JSON格式
            Map<String, Object> resultMap = JSON.parseObject(result, Map.class);
            assertEquals("user123_testSystem", resultMap.get("userInfo"));
            assertEquals(85, resultMap.get("riskScore"));
        }
    }

    @Test
    public void testExtractEventData_WithEmptyConfig() {
        try (MockedStatic<com.youxin.apollo.client.NacosClient> mockedNacosClient = 
             Mockito.mockStatic(com.youxin.apollo.client.NacosClient.class)) {
            
            mockedNacosClient.when(() -> 
                com.youxin.apollo.client.NacosClient.getByNameSpace(anyString(), eq("event.log.config"), anyString()))
                .thenReturn("");

            String result = EventLogConfigUtil.extractEventData(testEvent);
            
            assertEquals("", result);
        }
    }

    @Test
    public void testExtractEventData_WithNullEvent() {
        String result = EventLogConfigUtil.extractEventData(null);
        assertEquals("", result);
    }

    @Test
    public void testExtractEventData_WithEventCodeNotInConfig() {
        String mockConfig = "{\n" +
                "  \"OtherEvent\": {\n" +
                "    \"fields\": [\n" +
                "      {\n" +
                "        \"name\": \"sessionId\",\n" +
                "        \"expression\": \"$.params.sessionId\",\n" +
                "        \"type\": \"jsonpath\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";

        try (MockedStatic<com.youxin.apollo.client.NacosClient> mockedNacosClient = 
             Mockito.mockStatic(com.youxin.apollo.client.NacosClient.class)) {
            
            mockedNacosClient.when(() -> 
                com.youxin.apollo.client.NacosClient.getByNameSpace(anyString(), eq("event.log.config"), anyString()))
                .thenReturn(mockConfig);

            String result = EventLogConfigUtil.extractEventData(testEvent);
            
            assertEquals("", result);
        }
    }

    @Test
    public void testExtractEventData_WithInvalidJsonConfig() {
        String invalidConfig = "invalid json";

        try (MockedStatic<com.youxin.apollo.client.NacosClient> mockedNacosClient = 
             Mockito.mockStatic(com.youxin.apollo.client.NacosClient.class)) {
            
            mockedNacosClient.when(() -> 
                com.youxin.apollo.client.NacosClient.getByNameSpace(anyString(), eq("event.log.config"), anyString()))
                .thenReturn(invalidConfig);

            String result = EventLogConfigUtil.extractEventData(testEvent);
            
            assertEquals("", result);
        }
    }
}
