package com.youxin.risk.gateway.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.youxin.risk.commons.dao.gw.GwRequestMapper;
import com.youxin.risk.commons.service.ApolloClientAdapterWrapper;
import com.youxin.risk.gateway.mapper.GatewayMessageLogMapper;
import com.youxin.risk.gateway.task.xxljob.limit.condition.MessageLogQueryCondition;
import com.youxin.risk.gateway.vo.GatewayMessageLog;
import org.apache.ibatis.logging.LogFactory;
import org.apache.ibatis.logging.stdout.StdOutImpl;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {
        "classpath:spring/spring-config-test-new.xml"})
@Transactional(propagation = Propagation.REQUIRES_NEW, transactionManager = "gwTransactionManager")
//@Rollback(false)
public class GatewayMessageLogServiceImplTest {

    @Autowired
    private GatewayMessageLogServiceImpl gatewayMessageLogService;

    @Autowired
    private ApolloClientAdapterWrapper apolloClientAdapterWrapper;

    @Autowired
    private GatewayMessageLogMapper gatewayMessageLogMapper;



    @Before
    public void setUp() {
        LogFactory.useCustomLogging(StdOutImpl.class);
        //新增sql数据
        //模拟数据JD 5条  DXM 4条  其余的10条
        List<GatewayMessageLog> gatewayMessageLogList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildApiGatewayMessageLog("JD","ApiVerify"));
        }
        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildApiGatewayMessageLog("DXM","ApiVerify"));
        }
        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildApiGatewayMessageLog("OTHER", "ApiVerify"));
        }

        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildApiGatewayMessageLog("JD","ApiLendAudit"));
        }
        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildApiGatewayMessageLog("DXM","ApiLendAudit"));
        }

        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildHfqVerifyGatewayMessageLog("hfq03958300709460000"));
        }
        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildHfqVerifyGatewayMessageLog("hfq05958300709460000"));
        }

        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildHfqVerifyGatewayMessageLog("hfq05958300709460000"));
        }

        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildOtherGatewayMessageLog("haoHuanLendAudit"));
        }

        for (int i = 0; i < 10; i++) {
            gatewayMessageLogList.add(buildOtherGatewayMessageLog("haoHuanLendAuditReloan"));
        }


        gatewayMessageLogList.forEach(gatewayMessageLogMapper::insert);

        System.out.println(" setup done");
    }

//    @After
//    public void tearDown() {
//        //清空sql数据
////        gatewayMessageLogMapper.deleteAll();
//        testJdbcTemplate.execute("DELETE FROM gw_message_log where session_id = 'testSessionId'");
//    }



    @Test
    public void getListByCondition() {

        String config = "[\n" +
                "    {\n" +
                "        \"eventCode\": \"ApiVerify\",\n" +
                "        \"apiSource\": \"DXM\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventCode\": \"ApiVerify\",\n" +
                "        \"apiSource\": \"JD\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventCode\": \"haoHuanVerify\",\n" +
                "        \"originSourcePrefix\": \"hfq03\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventCode\": \"All\",\n" +
                "        \"limit\": 5\n" +
                "    }\n" +
                "]";
        List<JSONObject> stuckConfig = JSONArray.parseObject(config, new TypeReference<List<JSONObject>>() {});
        when(apolloClientAdapterWrapper.getListConfig(any(),
                any(),
                any())).thenReturn(Collections.singletonList(stuckConfig));

        List<GatewayMessageLog> listByCondition = gatewayMessageLogService.getListByPriority(stuckConfig);

        //验证查得数据 JD 5条  DXM 4条  其余1条
        assertEquals(5, listByCondition.size());
        assertEquals(5, listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains("DXM")).count());
    }

    @Test
    public void getListByCondition_2() {

        String config = "[\n" +
                "    {\n" +
                "        \"eventCode\": \"ApiVerify\",\n" +
                "        \"apiSource\": \"DXM\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventCode\": \"ApiVerify\",\n" +
                "        \"apiSource\": \"JD\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventCode\": \"haoHuanVerify\",\n" +
                "        \"originSourcePrefix\": \"hfq03\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventCode\": \"All\",\n" +
                "        \"limit\": 30\n" +
                "    }\n" +
                "]";
        List<JSONObject> stuckConfig = JSONArray.parseObject(config, new TypeReference<List<JSONObject>>() {});
        when(apolloClientAdapterWrapper.getListConfig(any(),
                any(),
                any())).thenReturn(Collections.singletonList(stuckConfig));

        List<GatewayMessageLog> listByCondition = gatewayMessageLogService.getListByPriority(stuckConfig);

        //验证查得数据 JD 5条  DXM 4条  其余1条
        assertEquals(30, listByCondition.size());
        assertEquals(10,
                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains("DXM")).count());
        assertEquals(10,
                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains(
                        "JD")).count());
        assertEquals(10,
                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains(
                        "hfq03")).count());
    }

    @Test
    public void getListByCondition_3() {

        String config = "[\n" +
                "    {\n" +
                "        \"eventCode\": \"ApiVerify\",\n" +
                "        \"apiSource\": \"DXM\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventCode\": \"ApiVerify\",\n" +
                "        \"apiSource\": \"JD\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventCode\": \"haoHuanVerify\",\n" +
                "        \"originSourcePrefix\": \"hfq03\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"limit\": 50\n" +
                "    }\n" +
                "]";
        List<JSONObject> stuckConfig = JSONArray.parseObject(config, new TypeReference<List<JSONObject>>() {});
        when(apolloClientAdapterWrapper.getListConfig(any(),
                any(),
                any())).thenReturn(Collections.singletonList(stuckConfig));

        List<GatewayMessageLog> listByCondition = gatewayMessageLogService.getListByPriority(stuckConfig);

        //验证查得数据 JD 5条  DXM 4条  其余1条
        assertEquals(50, listByCondition.size());
        assertEquals(10,
                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains("DXM")).count());
        assertEquals(10,
                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains(
                        "JD")).count());
        assertEquals(10,
                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains(
                        "hfq03")).count());

    }
//
//
//    @Test
//    public void getListByCondition_4() {
//
//        MessageLogQueryCondition messageLogQueryCondition = new MessageLogQueryCondition("ApiVerify");
//        messageLogQueryCondition.setLimit(10);
//        String config = "{\n" +
//                "    \"JD\":1,\n" +
//                "    \"DXM\":1,\n" +
//                "    \"HB\":1,\n" +
//                "    \"default\":10\n" +
//                "}";
//        when(apolloClientAdapterWrapper.getStringConfig(any(),
//                any(),
//                any())).thenReturn(config);
//
//        List<GatewayMessageLog> gatewayMessageLogList = new ArrayList<>();
//        for (int i = 0; i < 10; i++) {
//            gatewayMessageLogList.add(buildApiGatewayMessageLog("JD"));
//        }
//        for (int i = 0; i < 4; i++) {
//            gatewayMessageLogList.add(buildApiGatewayMessageLog("DXM"));
//        }
//        for (int i = 0; i < 5; i++) {
//            gatewayMessageLogList.add(buildApiGatewayMessageLog("HB"));
//        }
//        for (int i = 0; i < 10; i++) {
//            gatewayMessageLogList.add(buildApiGatewayMessageLog("OTHER"));
//        }
//        gatewayMessageLogList.forEach(gatewayMessageLogMapper::insert);
//
//        List<GatewayMessageLog> listByCondition = gatewayMessageLogService.getListByCondition(messageLogQueryCondition);
//
//        //验证查得数据 JD 5条  DXM 4条  其余1条
////        assertEquals(10, listByCondition.size());
//        assertEquals(10,
//                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains("JD")).count());
////        assertEquals(4, listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains("DXM")).count());
////        assertEquals(1,
////                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains("HB")).count());
////        assertEquals(0,
////                listByCondition.stream().filter(gatewayMessageLog -> gatewayMessageLog.getGatewayMessage().contains("OTHER")).count());
//    }


    private GatewayMessageLog buildApiGatewayMessageLog(String apiSource, String eventCode) {

        String gatewayMessage = "{\"params\":{\"apiSource\":\"%s\",\"icode\":\"0100010115\"," +
                "\"gatewayCreateTime\":1749037222974,\"sourceSystem\":\"HAO_HUAN\",\"occurTime\":\"20250604194022\"," +
                "\"targetSwitch\":\"ALL\",\"isBatchEvent\":false," +
                "\"sessionId\":\"20250604194022_A3011485822612028233\"," +
                "\"userKey\":\"c60e5b4965c4d492e68ea227eef560d3\",\"eventCode\":\"ApiVerify\",\"uid\":\"13744857\"," +
                "\"agencyRequestId\":\"20250604194022_eadffa3b914005112\"," +
                "\"loanKey\":\"hh_20250604194022_eadffa3b914005112\",\"loanId\":\"87313684\"}," +
                "\"variableRequestParams\":{},\"ruleSetMap\":{},\"dataId\":{},\"simpleData\":{},\"otherMaps\":{}," +
                "\"batch\":false,\"eventCodeForPoint\":\"ApiVerify\",\"mirror\":false," +
                "\"occurTime\":\"20250604194022\"}";
        return buildGatewayMessageLog(eventCode, String.format(gatewayMessage, apiSource));
    }

    private GatewayMessageLog buildHfqVerifyGatewayMessageLog(String originSourceWhole) {

        String gatewayMessage = "{\"params\":{\"appVersion\":\"897\",\"gatewayCreateTime\":1749541294725," +
                "\"wifiSSID\":\"三期805\",\"originSourceWhole\":\"%s\",\"occurTime\":\"20250610154134\"," +
                "\"channel\":\"huawei\",\"type\":\"MA\"," +
                "\"deviceId\":\"00000000-46cc-1789-ffff-ffffef05ac4a-2fb8f242bc2527e6\",\"deviceName\":\"E8B1AA\"," +
                "\"hasNotSettled\":\"false\",\"mobileModel\":\"HBN-AL00\",\"action\":\"audit\"," +
                "\"wifiLevel\":\"Android_4\",\"longitude\":\"116.325004\",\"icode\":\"0100030001\",\"ip\":\"113.101" +
                ".35.86\",\"currentBalance\":\"0\",\"loanCount\":\"0\",\"isMaxOverdueGt10d\":\"false\"," +
                "\"zaCustType\":\"1\",\"userKey\":\"703123cc5e50af5c4cecf0882501f8c1\"," +
                "\"batteryPlugType\":\"Android_0\",\"isOverdueGt1d\":\"false\",\"device\":\"HBN-AL00\"," +
                "\"isCopyPackage\":\"0\",\"loanId\":\"88199939\",\"androidid\":\"2fb8f242bc2527e6\"," +
                "\"mappingId\":\"51\",\"isSumOverdueGt3t\":\"false\",\"actionTime\":\"1749541294\"," +
                "\"sourceSystem\":\"HAO_HUAN\",\"latitude\":\"23.561822\",\"targetSwitch\":\"ALL\"," +
                "\"isBatchEvent\":false,\"platform\":\"Android\",\"userAdditionalInfo\":\"{\\\"amount\\\":\\\"\\\"," +
                "\\\"utmSource\\\":\\\"huawei\\\",\\\"isHaoleCardRight\\\":false,\\\"agentName\\\":\\\"华为-星艋\\\"," +
                "\\\"marketType\\\":\\\"MA\\\",\\\"isNewHaoLeCardRight\\\":false,\\\"retrialCount\\\":0," +
                "\\\"sourceType\\\":\\\"市场厂商\\\",\\\"retrialUseCount\\\":0,\\\"isMobileCancelled\\\":true," +
                "\\\"line_type\\\":\\\"\\\",\\\"alias\\\":\\\"HW-XM\\\",\\\"flow\\\":\\\"hh\\\"," +
                "\\\"isHaoleCard\\\":0}\",\"uid\":\"83015693\",\"auditUa\":\"Mozilla/5.0 (Linux; Android 12; HBN-AL00" +
                " Build/HUAWEIHBN-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.196 " +
                "Mobile Safari/537.36\",\"osVersion\":\"1\",\"userStatusInfo\":\"{\\\"phoneInfoStatus\\\":\\\"1\\\"," +
                "\\\"creditCardStatus\\\":\\\"0\\\",\\\"isShowCreditCard\\\":\\\"0\\\"," +
                "\\\"taobaoStatus\\\":\\\"null\\\",\\\"alipayTime\\\":\\\"0\\\",\\\"alipayStatus\\\":\\\"0\\\"," +
                "\\\"taobaoTime\\\":\\\"0\\\"}\",\"agencyRequestId\":\"20250610154134_ebf9989609d48a05f\"," +
                "\"loanKey\":\"hh_20250610154134_ebf9989609d48a05f\"," +
                "\"oaid\":\"5fe708ad-7359-4072-bac7-9d83b1edaa15\",\"channelCode\":\"huawei\"," +
                "\"batteryLevel\":\"62\",\"jailBroken\":\"0\",\"sessionId\":\"20250610154134_6004758399015703891\"," +
                "\"eventCode\":\"haoHuanVerify\",\"authVersion\":\"1\",\"auditIp\":\"*************\",\"imei\":\"\"," +
                "\"wifiMac\":\"02:00:00:00:00:00\",\"firstLoanTime\":\"\",\"flowType\":\"split\"}," +
                "\"variableRequestParams\":{},\"ruleSetMap\":{},\"dataId\":{},\"simpleData\":{},\"otherMaps\":{}," +
                "\"eventCodeForPoint\":\"haoHuanVerify\",\"batch\":false,\"occurTime\":\"20250610154134\"," +
                "\"mirror\":false}";
        return buildGatewayMessageLog("haoHuanVerify", String.format(gatewayMessage, originSourceWhole));
    }

    private GatewayMessageLog buildOtherGatewayMessageLog(String eventCode) {

        return buildGatewayMessageLog(eventCode, null);
    }
    private GatewayMessageLog buildGatewayMessageLog(String eventCode,String gatewayMessage) {
        GatewayMessageLog gatewayMessageLog = new GatewayMessageLog();
        gatewayMessageLog.setEventCode(eventCode);
        gatewayMessageLog.setUserKey("testUserKey");
        gatewayMessageLog.setSessionId("testSessionId");
        gatewayMessageLog.setCreateTime(new Date());
        gatewayMessageLog.setGatewayMessage(gatewayMessage);
        return gatewayMessageLog;
    }
}