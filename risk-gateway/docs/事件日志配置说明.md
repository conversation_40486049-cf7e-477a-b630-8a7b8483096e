# 事件日志配置说明

## 概述

本文档描述了在CallbackService中增加事件数据日志打印功能的配置方法。该功能允许通过Nacos配置来控制在回调日志中打印哪些事件字段，支持JSONPath和MVEL表达式两种字段提取方式。

## 功能特性

1. **事件维度配置**：按eventCode配置要打印的字段列表
2. **多种表达式支持**：支持JSONPath和MVEL表达式提取字段值
3. **字段级目标key配置**：每个字段可以配置自己的输出key名称
4. **灵活的字段映射**：支持自定义字段名称映射

## Nacos配置

### 配置Key
```
event.log.config
```

### 配置格式
```json
{
  "eventCode1": {
    "fields": [
      {
        "name": "字段名称",
        "expression": "提取表达式",
        "type": "表达式类型",
        "targetKey": "目标key名称（可选）"
      }
    ]
  }
}
```

### 配置参数说明

#### 事件配置
- **eventCode**: 事件代码，作为配置的key
- **fields**: 字段配置数组，定义要提取的字段

#### 字段配置
- **name**: 字段名称，用于内部标识
- **expression**: 字段提取表达式
- **type**: 表达式类型，支持 `jsonpath` 和 `mvel`，默认为 `jsonpath`
- **targetKey**: 可选，指定该字段在输出JSON中的key名称，如果不配置则使用name作为key

## 配置示例

### 示例1：基本JSONPath配置
```json
{
  "ApiVerify": {
    "fields": [
      {
        "name": "sessionId",
        "expression": "$.params.sessionId",
        "type": "jsonpath",
        "targetKey": "session"
      },
      {
        "name": "userKey",
        "expression": "$.params.userKey",
        "type": "jsonpath",
        "targetKey": "user"
      },
      {
        "name": "eventCode",
        "expression": "$.params.eventCode",
        "type": "jsonpath"
      }
    ]
  }
}
```

### 示例2：MVEL表达式配置
```json
{
  "haoHuanApply": {
    "fields": [
      {
        "name": "userId",
        "expression": "params.userKey",
        "type": "mvel",
        "targetKey": "user_id"
      },
      {
        "name": "riskScore",
        "expression": "verifyResult.riskScore",
        "type": "mvel",
        "targetKey": "risk_score"
      },
      {
        "name": "userInfo",
        "expression": "params.userKey + '_' + params.sourceSystem",
        "type": "mvel",
        "targetKey": "user_info"
      }
    ]
  }
}
```

### 示例3：混合配置
```json
{
  "loanApply": {
    "fields": [
      {
        "name": "sessionId",
        "expression": "$.params.sessionId",
        "type": "jsonpath"
      },
      {
        "name": "userInfo",
        "expression": "params.userKey + '_' + params.sourceSystem",
        "type": "mvel",
        "targetKey": "combined_user_info"
      },
      {
        "name": "dataVoKeys",
        "expression": "$.dataVo",
        "type": "jsonpath",
        "targetKey": "data_summary"
      }
    ]
  }
}
```

## 表达式语法

### JSONPath表达式
JSONPath表达式用于从Event对象的JSON表示中提取数据。

**常用语法：**
- `$.params.fieldName` - 提取params中的字段
- `$.dataVo.serviceName` - 提取dataVo中的数据
- `$.verifyResult.riskScore` - 提取验证结果中的字段
- `$.params.*` - 提取params中的所有字段

**示例：**
```json
{
  "name": "userKey",
  "expression": "$.params.userKey",
  "type": "jsonpath",
  "targetKey": "user_id"
}
```

### MVEL表达式
MVEL表达式提供更强大的数据处理能力，支持逻辑运算和字符串操作。

**可用变量：**
- `event` - Event对象本身
- `params` - event.getParams()
- `dataVo` - event.getDataVo()
- `verifyResult` - event.getVerifyResult()

**示例：**
```json
{
  "name": "userInfo",
  "expression": "params.userKey + '_' + params.sourceSystem",
  "type": "mvel",
  "targetKey": "user_system_info"
}
```

## 输出格式

### 配置targetKey的字段
```json
{
  "session": "12345",
  "user": "user123",
  "eventCode": "ApiVerify"
}
```

### 不配置targetKey的字段
```json
{
  "sessionId": "12345",
  "userKey": "user123",
  "eventCode": "ApiVerify"
}
```

## 日志输出示例

配置生效后，CallbackService的日志将包含eventData字段：

```
2024-01-15 10:30:45,123^|INFO^|pool-1-thread-1^|12345^|67890^|CallbackService^|log:301^|request=[sessionId=12345&userKey=user123&] callbackRet={"retCode":"0000"} plaintextRet={"result":"success"} cost=150 eventData={"session":"12345","user":"user123","eventCode":"ApiVerify"}
```

## 注意事项

1. **性能考虑**：表达式提取会增加一定的性能开销，建议只配置必要的字段
2. **异常处理**：表达式执行失败时会记录警告日志，但不会影响主流程
3. **配置热更新**：Nacos配置支持热更新，修改后立即生效
4. **字段安全**：避免提取敏感信息到日志中
5. **表达式验证**：建议在测试环境验证表达式的正确性
6. **targetKey优先级**：如果配置了targetKey，将使用targetKey作为输出key，否则使用name

## 故障排查

### 常见问题

1. **配置不生效**
   - 检查Nacos配置key是否正确：`event.log.config`
   - 检查JSON格式是否正确
   - 检查eventCode是否匹配

2. **字段提取失败**
   - 检查表达式语法是否正确
   - 检查字段路径是否存在
   - 查看日志中的警告信息

3. **性能问题**
   - 减少配置的字段数量
   - 优化表达式复杂度
   - 考虑使用更简单的JSONPath表达式

### 调试方法

1. 启用DEBUG日志级别查看详细信息
2. 使用简单的表达式测试配置
3. 检查Event对象的实际结构
