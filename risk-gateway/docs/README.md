# 事件日志配置功能

## 功能概述

在CallbackService的第194行日志打印中增加了`gatewayVo.getEvent()`数据的功能，通过Nacos配置控制打印哪些事件字段。

## 主要特性

1. **事件维度配置**：按eventCode配置要打印的字段列表
2. **多种表达式支持**：支持JSONPath和MVEL表达式提取字段值
3. **字段级目标key配置**：每个字段可以配置自己的输出key名称
4. **安全性**：表达式执行失败不影响主流程
5. **性能优化**：配置为空时快速返回

## 修改文件

### 核心文件
- `CallbackService.java` - 修改log方法，增加事件数据打印
- `EventLogConfigUtil.java` - 新增工具类，处理事件数据提取

### 测试文件
- `EventLogConfigUtilTest.java` - 基础功能测试

### 文档文件
- `事件日志配置说明.md` - 详细配置说明
- `event-log-config-example.json` - 配置示例

## 快速开始

### 1. Nacos配置

配置key: `event.log.config`

```json
{
  "ApiVerify": {
    "fields": [
      {
        "name": "sessionId",
        "expression": "$.params.sessionId",
        "type": "jsonpath",
        "targetKey": "session"
      },
      {
        "name": "userKey",
        "expression": "$.params.userKey",
        "type": "jsonpath",
        "targetKey": "user"
      }
    ]
  }
}
```

### 2. 日志输出效果

原日志：
```
request=[...] callbackRet=... plaintextRet=... cost=150
```

新日志：
```
request=[...] callbackRet=... plaintextRet=... cost=150 eventData={"session":"12345","user":"user123"}
```

## 配置说明

### 字段配置参数

- **name**: 字段名称，用于内部标识
- **expression**: 字段提取表达式
- **type**: 表达式类型，支持 `jsonpath` 和 `mvel`，默认为 `jsonpath`
- **targetKey**: 可选，指定该字段在输出JSON中的key名称，如果不配置则使用name作为key

### 表达式类型

#### JSONPath
- `$.params.userKey` - 提取params中的userKey字段
- `$.verifyResult.riskScore` - 提取验证结果中的riskScore字段

#### MVEL
- `params.userKey` - 提取params中的userKey字段
- `params.userKey + '_' + params.sourceSystem` - 字符串拼接

## 注意事项

1. **性能考虑**：只配置必要的字段
2. **安全性**：避免提取敏感信息到日志中
3. **配置验证**：建议在测试环境验证表达式的正确性
4. **热更新**：Nacos配置支持热更新，修改后立即生效

## 故障排查

1. 检查Nacos配置key是否正确：`event.log.config`
2. 检查JSON格式是否正确
3. 检查eventCode是否匹配
4. 查看日志中的警告信息
5. 启用DEBUG日志级别查看详细信息
