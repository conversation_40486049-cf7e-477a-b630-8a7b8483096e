{"ApiVerify": {"fields": [{"name": "sessionId", "expression": "$.params.sessionId", "type": "jsonpath", "targetKey": "session"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "$.params.userKey", "type": "jsonpath", "targetKey": "user"}, {"name": "eventCode", "expression": "$.params.eventCode", "type": "jsonpath", "targetKey": "event"}, {"name": "sourceSystem", "expression": "$.params.sourceSystem", "type": "jsonpath", "targetKey": "source"}, {"name": "occurTime", "expression": "$.params.occurTime", "type": "jsonpath", "targetKey": "time"}]}, "haoHuanApply": {"fields": [{"name": "userId", "expression": "params.user<PERSON>ey", "type": "mvel", "targetKey": "user_id"}, {"name": "loan<PERSON>ey", "expression": "params.loanKey", "type": "mvel", "targetKey": "loan_id"}, {"name": "riskScore", "expression": "verifyResult.riskScore", "type": "mvel", "targetKey": "risk_score"}, {"name": "riskLevel", "expression": "verifyResult.riskLevel", "type": "mvel", "targetKey": "risk_level"}, {"name": "processTime", "expression": "params.occurTime", "type": "mvel", "targetKey": "process_time"}, {"name": "userInfo", "expression": "params.userKey + '_' + params.sourceSystem", "type": "mvel", "targetKey": "user_system"}]}, "loanApply": {"fields": [{"name": "sessionId", "expression": "$.params.sessionId", "type": "jsonpath", "targetKey": "session"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "$.params.userKey", "type": "jsonpath", "targetKey": "user"}, {"name": "loanAmount", "expression": "$.params.loanAmount", "type": "jsonpath", "targetKey": "amount"}, {"name": "loanTerm", "expression": "$.params.loanTerm", "type": "jsonpath", "targetKey": "term"}, {"name": "finalDecision", "expression": "verifyResult.finalDecision", "type": "mvel", "targetKey": "decision"}, {"name": "riskScore", "expression": "verifyResult.riskScore", "type": "mvel", "targetKey": "score"}]}, "prepareHaoHuanApply": {"fields": [{"name": "sessionId", "expression": "$.params.sessionId", "type": "jsonpath"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "$.params.userKey", "type": "jsonpath"}, {"name": "preApprovalAmount", "expression": "$.verifyResult.preApprovalAmount", "type": "jsonpath", "targetKey": "pre_amount"}, {"name": "preApprovalResult", "expression": "$.verifyResult.preApprovalResult", "type": "jsonpath", "targetKey": "pre_result"}, {"name": "deviceInfo", "expression": "params.deviceId + '_' + params.appVersion", "type": "mvel", "targetKey": "device"}]}, "riskMonitor": {"fields": [{"name": "monitorType", "expression": "$.params.monitorType", "type": "jsonpath", "targetKey": "type"}, {"name": "alertLevel", "expression": "$.verifyResult.alertLevel", "type": "jsonpath", "targetKey": "level"}, {"name": "riskIndicators", "expression": "$.verifyResult.riskIndicators", "type": "jsonpath", "targetKey": "indicators"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "$.params.userKey", "type": "jsonpath", "targetKey": "user"}]}, "fraudDetection": {"fields": [{"name": "fraudScore", "expression": "verifyResult.fraudScore", "type": "mvel", "targetKey": "fraud_score"}, {"name": "fraudRules", "expression": "verifyResult.hitRules", "type": "mvel", "targetKey": "hit_rules"}, {"name": "deviceFingerprint", "expression": "params.deviceId", "type": "mvel", "targetKey": "device_id"}, {"name": "ipLocation", "expression": "params.ip + '_' + params.location", "type": "mvel", "targetKey": "ip_location"}, {"name": "userBeh<PERSON>or", "expression": "$.dataVo.behaviorAnalysis", "type": "jsonpath", "targetKey": "behavior"}]}}